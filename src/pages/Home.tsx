/**
 * 首页组件
 * 包含机器人欢迎界面和登录方式选择
 */

import React, { useState } from 'react';
import { Button } from '@heroui/button';
import SpineRobot from '../components/SpineRobot';

interface HomeProps {
  onCarLoginClick: () => void;
  onPhoneLoginClick: () => void;
}

const Home: React.FC<HomeProps> = ({ onCarLoginClick, onPhoneLoginClick }) => {
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  return (
    <div className="home-container bg-gradient-to-br from-gray-50 to-white flex flex-col">
      {/* 主要内容区域 - 改为自适应布局 */}
      <div className="flex-1 flex flex-col px-6 py-8 justify-between">
        <div className="w-full max-w-sm mx-auto flex-1 flex flex-col justify-center min-h-0">
          {/* 机器人欢迎区域 */}
          <div className="text-center mb-8 sm:mb-12">
            {/* 机器人图片区域 */}
            <div className="relative mb-8">
              {/* 装饰性浮动元素 */}
              <div className="absolute -top-4 -left-4 w-3 h-3 bg-blue-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '0s' }}></div>
              <div className="absolute -top-2 right-8 w-2 h-2 bg-red-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '0.5s' }}></div>
              <div className="absolute top-4 -right-2 w-2 h-2 bg-green-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '1s' }}></div>
              <div className="absolute -bottom-2 left-4 w-2 h-2 bg-yellow-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '1.5s' }}></div>
              <div className="absolute bottom-2 -right-4 w-3 h-3 bg-cyan-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '2s' }}></div>

              {/* 星星装饰 */}
              <div className="absolute -top-6 left-12 text-yellow-400 text-lg animate-pulse" style={{ animationDelay: '0.2s' }}>✨</div>
              <div className="absolute top-8 right-2 text-yellow-400 text-sm animate-pulse" style={{ animationDelay: '0.8s' }}>⭐</div>
              <div className="absolute -bottom-4 right-8 text-yellow-400 text-lg animate-pulse" style={{ animationDelay: '1.2s' }}>✨</div>
              <div className="absolute bottom-6 -left-2 text-yellow-400 text-sm animate-pulse" style={{ animationDelay: '1.8s' }}>⭐</div>

              {/* 机器人动画容器 */}
              <div className="w-48 h-48 mx-auto relative">
                {/* 背景光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-100 to-green-100 rounded-full opacity-30 animate-pulse"></div>

                {/* Spine 机器人动画 */}
                <div
                  className="w-full h-full relative z-10 flex items-center justify-center overflow-hidden"
                  style={{
                    borderRadius: '50%',
                  }}
                >
                  <div
                    style={{
                      transform: 'translateY(-40%)'
                    }}
                  >
                    <SpineRobot
                      animation="think"
                      className="drop-shadow-lg"
                      loop={true}
                      timeScale={1.0}
                      onError={(error) => {
                        console.error('Spine robot animation error:', error);
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* 欢迎文字区域 */}
            <div className="space-y-2">
              <p className="text-sm text-gray-500 leading-relaxed">
                你好，我是汽车街销售精灵！
              </p>
            </div>
          </div>

          {/* 登录按钮区域 */}
          <div className="space-y-4 mb-6 sm:mb-8">
            {/* 汽车街登录按钮 */}
            <Button
              variant="solid"
              size="lg"
              className={`w-full h-14 text-base font-semibold text-white flat-button transition-all duration-200 ${
                agreedToTerms
                  ? 'shadow-lg hover:shadow-xl transform hover:scale-[1.02]'
                  : 'opacity-50 cursor-not-allowed'
              }`}
              style={{ backgroundColor: '#637480' }}
              isDisabled={!agreedToTerms}
              onPress={agreedToTerms ? onCarLoginClick : undefined}
            >
              <div className="flex items-center justify-center space-x-2">
                <span>通过汽车街登录</span>
              </div>
            </Button>

            {/* 手机号验证登录按钮 */}
            <Button
              variant="solid"
              size="lg"
              className={`w-full h-14 text-base font-semibold text-white flat-button transition-all duration-200 ${
                agreedToTerms
                  ? 'shadow-lg hover:shadow-xl transform hover:scale-[1.02]'
                  : 'opacity-50 cursor-not-allowed'
              }`}
              style={{ backgroundColor: '#00A76F' }}
              isDisabled={!agreedToTerms}
              onPress={agreedToTerms ? onPhoneLoginClick : undefined}
            >
              <div className="flex items-center justify-center space-x-2">
                <span>通过手机号验证登录</span>
              </div>
            </Button>
          </div>
        </div>

        {/* 服务协议区域 - 移到主内容区域内，确保可见 */}
        <div className="w-full max-w-sm mx-auto px-0 pb-0 pt-4">
          <div className="bg-gray-50/50 rounded-2xl p-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
              <button
                className={`w-6 h-6 rounded-md border-2 flex items-center justify-center transition-all duration-300 touch-manipulation ${
                  agreedToTerms
                    ? 'border-[#00A76F] scale-105 shadow-sm'
                    : 'border-gray-300 bg-white hover:border-[#00A76F] hover:shadow-sm'
                }`}
                style={{
                  backgroundColor: agreedToTerms ? '#00A76F' : 'white'
                }}
                onClick={() => setAgreedToTerms(!agreedToTerms)}
                aria-label={agreedToTerms ? '已同意服务协议' : '点击同意服务协议'}
              >
                {agreedToTerms && (
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    aria-hidden="true"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                )}
              </button>
              </div>
              <div className="text-sm text-gray-600 leading-relaxed flex-1">
              <span>已经阅读并同意汽车街的</span>
              <button
                className="mx-1 underline cursor-pointer hover:text-[#00A76F] transition-colors font-medium touch-manipulation"
                style={{ color: '#00A76F' }}
                onClick={() => {
                  // 这里可以添加打开服务协议的逻辑
                  console.log('打开服务协议');
                }}
                aria-label="查看服务协议"
              >
                服务协议
              </button>
              <span>和</span>
              <button
                className="mx-1 underline cursor-pointer hover:text-[#00A76F] transition-colors font-medium touch-manipulation"
                style={{ color: '#00A76F' }}
                onClick={() => {
                  // 这里可以添加打开隐私政策的逻辑
                  console.log('打开隐私政策');
                }}
                aria-label="查看隐私政策"
              >
                隐私政策
              </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
