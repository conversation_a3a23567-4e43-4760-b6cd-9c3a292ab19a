/**
 * 设置页面组件
 * 包含个人信息、智能精灵、系统相关等设置项
 */

import React, { useState, useEffect } from 'react';
import AvatarDrawer from '../components/AvatarDrawer';
import InputDrawer from '../components/InputDrawer';
import CityPickerDrawer from '../components/CityPickerDrawer';
import RobotSelectorDrawer from '../components/RobotSelectorDrawer';
import LanguageSelectorDrawer from '../components/LanguageSelectorDrawer';
import ConfirmModal from '../components/ConfirmModal';
import SpineRobot from '../components/SpineRobot';
import { getAvatar } from '../utils/avatarUtils';
import {
  getNickname,
  saveNickname,
  getPhone,
  savePhone,
  getCity,
  saveCity,
  getRobotAvatar,
  saveRobotAvatar,
  getLanguage,
  saveLanguage,
  validateNickname,
  validatePhone
} from '../utils/userInfoUtils';
import { clearAllChatRecords, logout } from '../utils/dataCleanup';
import { Province, City } from '../data/chinaAreaData';
import { RobotAvatar } from '../data/robotAvatars';
import { Language, getLanguageByCode } from '../data/languages';

interface SettingsPageProps {
  onBack: () => void;
  onLogout?: () => void;
  onCarRecommendationHistoryClick?: () => void;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onBack, onLogout, onCarRecommendationHistoryClick }) => {
  const [isAvatarDrawerOpen, setIsAvatarDrawerOpen] = useState(false);
  const [isNicknameDrawerOpen, setIsNicknameDrawerOpen] = useState(false);
  const [isPhoneDrawerOpen, setIsPhoneDrawerOpen] = useState(false);
  const [isCityDrawerOpen, setIsCityDrawerOpen] = useState(false);
  const [isRobotDrawerOpen, setIsRobotDrawerOpen] = useState(false);
  const [isLanguageDrawerOpen, setIsLanguageDrawerOpen] = useState(false);

  // 确认模态框状态
  const [isClearRecordsModalOpen, setIsClearRecordsModalOpen] = useState(false);
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const [currentAvatar, setCurrentAvatar] = useState<string | null>(null);
  const [currentNickname, setCurrentNickname] = useState<string>('');
  const [currentPhone, setCurrentPhone] = useState<string>('');
  const [currentCity, setCurrentCity] = useState<string>('');
  const [currentRobotId, setCurrentRobotId] = useState<string>('');
  const [currentLanguageCode, setCurrentLanguageCode] = useState<string>('');

  // 组件挂载时加载用户信息
  useEffect(() => {
    const savedAvatar = getAvatar();
    const savedNickname = getNickname();
    const savedPhone = getPhone();
    const savedCity = getCity();
    const savedRobotId = getRobotAvatar();
    const savedLanguageCode = getLanguage();

    setCurrentAvatar(savedAvatar);
    setCurrentNickname(savedNickname);
    setCurrentPhone(savedPhone);
    setCurrentCity(savedCity);
    setCurrentRobotId(savedRobotId);
    setCurrentLanguageCode(savedLanguageCode);
  }, []);

  // 处理头像更改
  const handleAvatarChange = (base64Image: string) => {
    setCurrentAvatar(base64Image);
  };

  // 处理昵称保存
  const handleNicknameSave = async (nickname: string) => {
    saveNickname(nickname);
    setCurrentNickname(nickname);
  };

  // 处理电话保存
  const handlePhoneSave = async (phone: string) => {
    savePhone(phone);
    setCurrentPhone(phone);
  };

  // 处理城市选择
  const handleCitySelect = (_province: Province, city: City) => {
    const cityName = city.name;
    saveCity(cityName);
    setCurrentCity(cityName);
  };

  // 处理机器人选择
  const handleRobotSelect = (robot: RobotAvatar) => {
    saveRobotAvatar(robot.id);
    setCurrentRobotId(robot.id);
  };

  // 处理语言选择
  const handleLanguageSelect = (language: Language) => {
    saveLanguage(language.code);
    setCurrentLanguageCode(language.code);
  };

  // 打开头像抽屉
  const handleAvatarClick = () => {
    setIsAvatarDrawerOpen(true);
  };

  // 打开昵称抽屉
  const handleNicknameClick = () => {
    setIsNicknameDrawerOpen(true);
  };

  // 打开电话抽屉
  const handlePhoneClick = () => {
    setIsPhoneDrawerOpen(true);
  };

  // 打开城市抽屉
  const handleCityClick = () => {
    setIsCityDrawerOpen(true);
  };

  // 打开机器人抽屉
  const handleRobotClick = () => {
    setIsRobotDrawerOpen(true);
  };

  // 打开语言抽屉
  const handleLanguageClick = () => {
    setIsLanguageDrawerOpen(true);
  };

  // 关闭抽屉
  const handleAvatarDrawerClose = () => {
    setIsAvatarDrawerOpen(false);
  };

  const handleNicknameDrawerClose = () => {
    setIsNicknameDrawerOpen(false);
  };

  const handlePhoneDrawerClose = () => {
    setIsPhoneDrawerOpen(false);
  };

  const handleCityDrawerClose = () => {
    setIsCityDrawerOpen(false);
  };

  const handleRobotDrawerClose = () => {
    setIsRobotDrawerOpen(false);
  };

  const handleLanguageDrawerClose = () => {
    setIsLanguageDrawerOpen(false);
  };

  // 清除所有记录相关处理函数
  const handleClearRecordsClick = () => {
    setIsClearRecordsModalOpen(true);
  };

  const handleClearRecordsConfirm = async () => {
    setIsProcessing(true);
    try {
      clearAllChatRecords();
      // 清除成功后关闭模态框
      setIsClearRecordsModalOpen(false);
      // 可以添加成功提示
      console.log('清除聊天记录成功');
    } catch (error) {
      console.error('清除聊天记录失败:', error);
      // 可以添加错误提示
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClearRecordsCancel = () => {
    setIsClearRecordsModalOpen(false);
  };

  // 车辆推荐记录点击处理
  const handleCarRecommendationHistoryClick = () => {
    if (onCarRecommendationHistoryClick) {
      onCarRecommendationHistoryClick();
    }
  };

  // 账号登出相关处理函数
  const handleLogoutClick = () => {
    setIsLogoutModalOpen(true);
  };

  const handleLogoutConfirm = async () => {
    setIsProcessing(true);
    try {
      logout();
      console.log('登出成功');
      // 立即调用父组件的登出回调，返回登录页面
      if (onLogout) {
        onLogout();
      }
    } catch (error) {
      console.error('登出失败:', error);
      // 可以添加错误提示
      setIsProcessing(false);
    }
    // 注意：不需要在finally中设置setIsProcessing(false)，因为组件会被卸载
  };

  const handleLogoutCancel = () => {
    setIsLogoutModalOpen(false);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="flex items-center px-4 py-3">
          <button
            onClick={onBack}
            className="mr-3 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800">设置</h1>
        </div>
      </div>

      {/* 设置内容 */}
      <div className="p-4 space-y-6">
        {/* 个人信息分组 */}
        <div className="bg-white rounded-xl overflow-hidden shadow-sm">
          <div className="px-4 py-3 bg-gray-50">
            <h2 className="text-sm font-medium text-gray-500">个人信息</h2>
          </div>
          
          {/* 头像 */}
          <button
            className="w-full px-4 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            onClick={handleAvatarClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">头像</span>
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 rounded-full bg-pink-100 flex items-center justify-center overflow-hidden">
                  {currentAvatar ? (
                    <img
                      src={currentAvatar}
                      alt="用户头像"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <svg className="w-6 h-6 text-pink-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  )}
                </div>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </button>

          {/* 昵称 */}
          <button
            className="w-full px-4 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            onClick={handleNicknameClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">昵称</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-500">{currentNickname}</span>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </button>

          {/* 联系电话 */}
          <button
            className="w-full px-4 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            onClick={handlePhoneClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">联系电话</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-500">{currentPhone}</span>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </button>

          {/* 意向看车城市 */}
          <button
            className="w-full px-4 py-4 hover:bg-gray-50 transition-colors"
            onClick={handleCityClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">意向看车城市</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-500">{currentCity}</span>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </button>
        </div>

        {/* 智能精灵分组 */}
        <div className="bg-white rounded-xl overflow-hidden shadow-sm">
          <div className="px-4 py-3 bg-gray-50">
            <h2 className="text-sm font-medium text-gray-500">智能精灵</h2>
          </div>
          
          {/* 形象 */}
          <button
            className="w-full px-4 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            onClick={handleRobotClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">形象</span>
              <div className="flex items-center space-x-2">
                {/* 显示当前机器人Spine动画 */}
                <div className="w-10 h-10 rounded-lg overflow-hidden flex items-center justify-center bg-gray-100">
                  <div style={{
                    transform: 'scale(0.18) translateY(-45%)',
                    transformOrigin: 'center'
                  }}>
                    <SpineRobot
                      animation={currentRobotId === 'blue' ? 'think' : 'listen'}
                      className=""
                      loop={true}
                      timeScale={1.0}
                      onError={(error) => {
                        console.error('Settings page spine animation error:', error);
                      }}
                    />
                  </div>
                </div>
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </button>

          {/* 语言 */}
          <button
            className="w-full px-4 py-4 hover:bg-gray-50 transition-colors"
            onClick={handleLanguageClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">语言</span>
              <div className="flex items-center space-x-2">
                {/* 显示当前语言名称 */}
                {(() => {
                  const currentLanguage = getLanguageByCode(currentLanguageCode);
                  return (
                    <span className="text-gray-500">
                      {currentLanguage ? currentLanguage.name : '中文'}
                    </span>
                  );
                })()}
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          </button>
        </div>

        {/* 系统相关分组 */}
        <div className="bg-white rounded-xl overflow-hidden shadow-sm">
          <div className="px-4 py-3 bg-gray-50">
            <h2 className="text-sm font-medium text-gray-500">系统相关</h2>
          </div>
          
          {/* 车辆推荐记录 */}
          <button
            className="w-full px-4 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            onClick={handleCarRecommendationHistoryClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">车辆推荐记录</span>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </button>

          {/* 清除所有记录 */}
          <button
            className="w-full px-4 py-4 border-b border-gray-100 hover:bg-gray-50 transition-colors"
            onClick={handleClearRecordsClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">清除所有记录</span>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </button>

          {/* 账号登出 */}
          <button
            className="w-full px-4 py-4 hover:bg-gray-50 transition-colors"
            onClick={handleLogoutClick}
          >
            <div className="flex items-center justify-between min-h-[44px]">
              <span className="text-gray-800 font-medium">账号登出</span>
              <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </button>
        </div>
      </div>

      {/* 底部客服信息 */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="text-center">
          <p className="text-sm text-gray-500">
            汽车街客服热线：<span className="text-[#00A76F] font-medium">400-123-4567</span>
          </p>
        </div>
      </div>

      {/* 底部安全区域 */}
      <div className="h-20"></div>

      {/* 头像修改抽屉 */}
      <AvatarDrawer
        isOpen={isAvatarDrawerOpen}
        onClose={handleAvatarDrawerClose}
        onAvatarChange={handleAvatarChange}
      />

      {/* 昵称修改抽屉 */}
      <InputDrawer
        isOpen={isNicknameDrawerOpen}
        onClose={handleNicknameDrawerClose}
        title="修改昵称"
        value={currentNickname}
        placeholder="请输入昵称"
        onSave={handleNicknameSave}
        inputType="text"
        maxLength={20}
        validator={validateNickname}
      />

      {/* 电话修改抽屉 */}
      <InputDrawer
        isOpen={isPhoneDrawerOpen}
        onClose={handlePhoneDrawerClose}
        title="修改联系电话"
        value={currentPhone}
        placeholder="请输入电话号码"
        onSave={handlePhoneSave}
        inputType="tel"
        maxLength={20}
        validator={validatePhone}
      />

      {/* 城市选择抽屉 */}
      <CityPickerDrawer
        isOpen={isCityDrawerOpen}
        onClose={handleCityDrawerClose}
        onCitySelect={handleCitySelect}
        currentCity={currentCity}
      />

      {/* 机器人选择抽屉 */}
      <RobotSelectorDrawer
        isOpen={isRobotDrawerOpen}
        onClose={handleRobotDrawerClose}
        onRobotSelect={handleRobotSelect}
        currentRobotId={currentRobotId}
      />

      {/* 语言选择抽屉 */}
      <LanguageSelectorDrawer
        isOpen={isLanguageDrawerOpen}
        onClose={handleLanguageDrawerClose}
        onLanguageSelect={handleLanguageSelect}
        currentLanguageCode={currentLanguageCode}
      />

      {/* 清除所有记录确认模态框 */}
      <ConfirmModal
        isOpen={isClearRecordsModalOpen}
        onClose={handleClearRecordsCancel}
        onConfirm={handleClearRecordsConfirm}
        title="清除所有记录"
        content="删除后历史对话记录将不可恢复，请确认是否继续删除？"
        confirmText="确认"
        cancelText="取消"
        confirmButtonColor="#00A76F"
        isLoading={isProcessing}
      />

      {/* 账号登出确认模态框 */}
      <ConfirmModal
        isOpen={isLogoutModalOpen}
        onClose={handleLogoutCancel}
        onConfirm={handleLogoutConfirm}
        title="账号登出"
        content="登出后需要重新登录才能使用，确认要登出吗？"
        confirmText="确认登出"
        cancelText="取消"
        confirmButtonColor="#00A76F"
        isLoading={isProcessing}
      />
    </div>
  );
};

export default SettingsPage;
