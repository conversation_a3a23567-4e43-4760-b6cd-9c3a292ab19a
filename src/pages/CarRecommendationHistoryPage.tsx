/**
 * 车辆推荐记录页面
 * 按日期分组显示所有车辆推荐记录
 */

import React, { useState, useEffect } from 'react';
import { getGroupedCarRecommendations, type CarRecommendationRecord } from '../utils/conversationStorage';

interface CarRecommendationHistoryPageProps {
  onBack: () => void;
  onCarDetailClick?: (carData: any) => void;
}

// 车辆卡片组件
const CarRecommendationCard: React.FC<{ car: any; onCarDetailClick?: (carData: any) => void }> = ({
  car,
  onCarDetailClick
}) => {
  if (!car) return null;

  // 生成图片URL
  const imageUrl = car.pic_url ?? (car.first_photo_url ? `http://images.autostreets.com/${car.first_photo_url}` : null);

  const handleCardClick = () => {
    if (onCarDetailClick) {
      onCarDetailClick(car);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleCardClick();
    }
  };

  return (
    <button
      className="w-full bg-white rounded-lg p-2 border border-gray-200 hover:bg-gray-50 transition-colors text-left"
      onClick={handleCardClick}
      onKeyDown={handleKeyDown}
      type="button"
    >
      <div className="flex space-x-3">
        {/* 车辆图片 */}
        <div className="flex-shrink-0">
          <div className="w-28 h-20 bg-gray-100 rounded-lg overflow-hidden">
            {imageUrl ? (
              <img
                src={imageUrl}
                alt={car.vehicle_name ?? '车辆图片'}
                className="w-full h-full object-cover"
                loading="lazy"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                暂无图片
              </div>
            )}
          </div>
        </div>

        {/* 车辆信息 */}
        <div className="flex-1 min-w-0 flex flex-col justify-center">
          {/* 车辆名称 */}
          <h3 className="text-xs font-semibold text-gray-800 leading-tight line-clamp-2 mb-1.5">
            {car.vehicle_name ?? '未知车型'}
          </h3>

          {/* 车辆详细信息 - 3列布局 */}
          <div className="grid grid-cols-3 gap-1.5 text-xs">
            <div className="text-center">
              <div className="text-[#00A76F] font-bold text-xs">
                {car.price ? `${(car.price / 10000).toFixed(1)}万元` : '价格面议'}
              </div>
              <div className="text-gray-500 mt-0.5 text-xs">价格</div>
            </div>
            <div className="text-center">
              <div className="text-gray-800 font-medium text-xs">
                {car.model_year ?? '未知'}年
              </div>
              <div className="text-gray-500 mt-0.5 text-xs">上牌时间</div>
            </div>
            <div className="text-center">
              <div className="text-gray-800 font-medium text-xs">
                {car.display_mileage ? `${(car.display_mileage / 10000).toFixed(1)}万公里` : '未知'}
              </div>
              <div className="text-gray-500 mt-0.5 text-xs">行驶里程</div>
            </div>
          </div>
        </div>
      </div>
    </button>
  );
};

const CarRecommendationHistoryPage: React.FC<CarRecommendationHistoryPageProps> = ({
  onBack,
  onCarDetailClick
}) => {
  const [groupedRecommendations, setGroupedRecommendations] = useState<Record<string, CarRecommendationRecord[]>>({});
  const [isLoading, setIsLoading] = useState(true);

  // 加载推荐记录
  useEffect(() => {
    const loadRecommendations = () => {
      try {
        const grouped = getGroupedCarRecommendations();
        setGroupedRecommendations(grouped);
      } catch (error) {
        console.error('加载推荐记录失败:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadRecommendations();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm sticky top-0 z-50">
        <div className="flex items-center px-4 py-3">
          <button
            onClick={onBack}
            className="mr-3 p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-800">车辆推荐记录</h1>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="p-4">
        {isLoading ? (
          // 加载状态
          <div className="flex items-center justify-center py-20">
            <div className="text-gray-500">加载中...</div>
          </div>
        ) : Object.keys(groupedRecommendations).length === 0 ? (
          // 空状态
          <div className="flex flex-col items-center justify-center py-20">
            <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p className="text-gray-500 text-center">
              暂无车辆推荐记录<br />
              <span className="text-sm">开始与AI对话获取车辆推荐吧</span>
            </p>
          </div>
        ) : (
          // 推荐记录列表
          <div className="space-y-6">
            {Object.entries(groupedRecommendations).map(([dateKey, records]) => (
              <div key={dateKey}>
                {/* 日期标题 */}
                <div className="text-center mb-4">
                  <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {dateKey}
                  </span>
                </div>

                {/* 该日期下的推荐记录 */}
                <div className="space-y-3">
                  {records.map((record) => (
                    <div key={record.id}>
                      {record.recommendations.map((car, carIndex) => (
                        <div key={`${record.id}-${carIndex}`} className="mb-3">
                          <CarRecommendationCard car={car} onCarDetailClick={onCarDetailClick} />
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部安全区域 */}
      <div className="h-20"></div>
    </div>
  );
};

export default CarRecommendationHistoryPage;
