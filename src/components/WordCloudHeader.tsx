import React, { useState, useEffect, useMemo, useRef } from 'react';
import WaveAnimation from './WaveAnimation';
import SpineRobot from './SpineRobot';
import {
  getWordCloudForConversation,
  saveWordCloudForConversation,
  type WordCloudItem
} from '../utils/wordCloudCache';

interface WordCloudHeaderProps {
  activeTaskType?: string | null;
  slotsData?: Record<string, any> | null;
  conversationId?: string | null; // 用于检测会话切换
  // 聊天状态相关属性
  isRecording?: boolean;
  isProcessingVoice?: boolean;
  isAIResponding?: boolean;
  currentPlayingMessageId?: string | null;
}

const WordCloudHeader: React.FC<WordCloudHeaderProps> = React.memo(({
  activeTaskType,
  slotsData,
  conversationId,
  isRecording = false,
  isProcessingVoice = false,
  isAIResponding = false,
  currentPlayingMessageId = null
}) => {
  // 累积的词汇状态
  const [accumulatedWords, setAccumulatedWords] = useState<WordCloudItem[]>([]);
  // 用于跟踪上一个会话ID
  const previousConversationIdRef = useRef<string | null>(null);
  // 用于跟踪会话切换状态，防止在切换过程中处理旧会话的数据
  const isSessionSwitchingRef = useRef<boolean>(false);

  // 初始化时确保词云数据正确
  useEffect(() => {
    if (conversationId && !previousConversationIdRef.current) {
      console.log('WordCloudHeader: 初始化，加载会话词云数据', { conversationId });
      const savedWords = getWordCloudForConversation(conversationId);
      setAccumulatedWords(savedWords);
      previousConversationIdRef.current = conversationId;
    }
  }, [conversationId]);

  // 计算当前应该显示的动画类型
  const getAnimationType = (): 'think' | 'listen' | 'listen2' | 'talk' => {
    // 优先级：talk > listen2 > listen > think
    if (currentPlayingMessageId) {
      console.log('🎵 Spine动画切换到 talk - TTS播放中:', currentPlayingMessageId);
      return 'talk'; // AI正在播放语音内容
    }
    if (isRecording) {
      console.log('🎤 Spine动画切换到 listen2 - 用户录音中');
      return 'listen2'; // 用户正在录音
    }
    if (isProcessingVoice) {
      console.log('🎧 Spine动画切换到 listen - 处理语音中');
      return 'listen'; // 处理语音输入
    }
    if (isAIResponding) {
      console.log('🤔 Spine动画切换到 think - AI响应中');
      return 'think'; // AI正在返回内容时显示思考状态
    }
    console.log('😌 Spine动画保持 think - 空闲状态');
    return 'think'; // 默认思考状态（空闲时）
  };

  const currentAnimationType = getAnimationType();

  // 从slots数据中提取词汇
  const getWordsFromSlots = (slots: Record<string, any> | null): string[] => {
    if (!slots) return [];

    const words: string[] = [];
    Object.entries(slots).forEach(([key, value]) => {
      if (typeof value === 'string' && value.trim()) {
        const cleanValue = value.trim();

        // 处理不同类型的值
        if (key === 'price') {
          // 价格信息：提取数字和单位
          const priceMatch = cleanValue.match(/(\d+(?:\.\d+)?)\s*(万|元|千)/g);
          if (priceMatch) {
            words.push(...priceMatch);
          } else {
            // 如果没有匹配到标准格式，直接添加原值
            words.push(cleanValue);
          }
        } else if (key === 'brand' || key === 'category') {
          // 品牌和类别：直接添加
          words.push(cleanValue);
        } else if (key.includes('vehicle_name')) {
          // 车辆名称：分割品牌和型号
          const parts = cleanValue.split(/\s+/);
          words.push(...parts);
        } else {
          // 其他类型：尝试智能分割
          if (cleanValue.length <= 10) {
            // 短文本直接添加
            words.push(cleanValue);
          } else {
            // 长文本分割
            const parts = cleanValue.split(/[\s,，、]+/);
            words.push(...parts.filter(part => part.length > 0 && part.length <= 8));
          }
        }
      } else if (typeof value === 'number') {
        words.push(value.toString());
      }
    });

    return words.filter(word => word.length > 0 && word.length <= 10); // 过滤过长的词汇
  };

  // 会话切换时的缓存管理 - 只在conversationId变化时执行
  useEffect(() => {
    // 确保 conversationId 有效
    if (!conversationId) {
      console.log('WordCloudHeader: conversationId 为空，清空词云显示');
      setAccumulatedWords([]);
      isSessionSwitchingRef.current = false;
      return;
    }

    // 只在会话ID真正变化时执行
    if (conversationId !== previousConversationIdRef.current) {
      console.log('WordCloudHeader: 会话切换', {
        from: previousConversationIdRef.current,
        to: conversationId
      });

      // 标记会话切换开始，防止处理旧会话的数据
      isSessionSwitchingRef.current = true;

      // 保存旧会话的词云数据（如果有的话）
      const previousConversationId = previousConversationIdRef.current;
      if (previousConversationId && accumulatedWords.length > 0) {
        console.log('WordCloudHeader: 保存旧会话词云数据', {
          conversationId: previousConversationId,
          wordCount: accumulatedWords.length
        });
        saveWordCloudForConversation(previousConversationId, accumulatedWords);
      }

      // 先清空当前词云，确保不会混合数据
      setAccumulatedWords([]);

      // 使用 setTimeout 确保状态更新完成后再加载新数据
      setTimeout(() => {
        // 再次检查会话ID是否仍然匹配（防止快速切换）
        if (conversationId === previousConversationIdRef.current) {
          // 然后加载新会话的词云数据
          const savedWords = getWordCloudForConversation(conversationId);
          console.log('WordCloudHeader: 加载新会话词云数据', {
            conversationId,
            wordCount: savedWords.length
          });

          // 设置新会话的词云数据
          setAccumulatedWords(savedWords);
        }

        // 标记会话切换结束
        isSessionSwitchingRef.current = false;
      }, 0);

      // 更新引用
      previousConversationIdRef.current = conversationId;
    }
  }, [conversationId]); // 只依赖conversationId，确保只在会话切换时执行

  // 仅在词汇变化时保存当前会话数据（避免会话切换时的重复保存）
  useEffect(() => {
    // 只有在会话ID稳定且有词汇数据时才保存
    // 添加额外检查：确保不是在会话切换过程中
    if (conversationId &&
        conversationId === previousConversationIdRef.current &&
        accumulatedWords.length > 0 &&
        !isSessionSwitchingRef.current) { // 关键修复：确保不在会话切换过程中

      console.log('WordCloudHeader: 词汇变化，保存当前会话词云数据', {
        conversationId,
        wordCount: accumulatedWords.length,
        words: accumulatedWords.map(w => w.word),
        isSessionSwitching: isSessionSwitchingRef.current
      });

      saveWordCloudForConversation(conversationId, accumulatedWords);
    }
  }, [accumulatedWords]); // 只依赖 accumulatedWords，避免会话切换时触发

  // 当有新的slots数据时，增量更新词汇并保存到缓存
  useEffect(() => {
    // 确保所有必要的参数都存在，且会话ID已经稳定
    // 添加额外检查：确保不是在会话切换过程中处理旧数据
    if (slotsData &&
        activeTaskType &&
        conversationId &&
        conversationId === previousConversationIdRef.current &&
        !isSessionSwitchingRef.current) { // 确保不在会话切换过程中

      const newWords = getWordsFromSlots(slotsData);
      if (newWords.length > 0) {
        console.log('WordCloudHeader: 添加新词汇到当前会话', {
          conversationId,
          newWords,
          activeTaskType,
          previousConversationId: previousConversationIdRef.current,
          isSessionSwitching: isSessionSwitchingRef.current
        });

        // 双重检查：确保当前会话ID与引用中的会话ID一致
        if (conversationId !== previousConversationIdRef.current) {
          console.warn('WordCloudHeader: 会话ID不一致，跳过词云更新', {
            conversationId,
            previousConversationId: previousConversationIdRef.current
          });
          return;
        }

        // 再次检查是否在会话切换过程中
        if (isSessionSwitchingRef.current) {
          console.warn('WordCloudHeader: 会话切换中，跳过词云更新');
          return;
        }

        // 先获取当前会话的最新词云数据（确保使用最新数据）
        const currentWords = getWordCloudForConversation(conversationId);

        // 创建新的词云项
        const newItems = newWords.map(word => ({
          word: word.trim(),
          id: `${activeTaskType}-${word}-${Date.now()}-${Math.random()}`,
          timestamp: Date.now(),
          source: activeTaskType,
        })).filter(item => item.word.length > 0);

        // 去重：基于word内容
        const existingWordsSet = new Set(currentWords.map(item => item.word));
        const filteredNewItems = newItems.filter(item => !existingWordsSet.has(item.word));

        // 合并并限制数量
        const combined = [...currentWords, ...filteredNewItems];
        const limited = combined.slice(-20); // 最多保留20个词

        // 保存更新后的词云
        saveWordCloudForConversation(conversationId, limited);

        // 更新状态
        setAccumulatedWords(limited);
      }
    }
  }, [slotsData, activeTaskType, conversationId]);

  // 组件卸载时保存当前会话的词云数据
  useEffect(() => {
    return () => {
      // 清理函数：组件卸载时保存当前会话数据
      // 如果正在会话切换中，不执行保存操作
      if (isSessionSwitchingRef.current) {
        console.log('WordCloudHeader: 组件卸载时正在会话切换中，跳过保存');
        return;
      }

      const currentConversationId = previousConversationIdRef.current;
      if (currentConversationId && accumulatedWords.length > 0) {
        console.log('WordCloudHeader: 组件卸载，保存当前会话词云数据', {
          conversationId: currentConversationId,
          wordCount: accumulatedWords.length,
          words: accumulatedWords.map(w => w.word)
        });

        // 确保保存的是当前会话的数据
        const currentWords = getWordCloudForConversation(currentConversationId);
        const combinedWords = [...currentWords, ...accumulatedWords.filter(
          item => !currentWords.some(w => w.word === item.word)
        )];

        // 限制数量并保存
        const limitedWords = combinedWords.slice(-20);
        saveWordCloudForConversation(currentConversationId, limitedWords);
      }
    };
  }, [accumulatedWords]); // 依赖accumulatedWords，确保使用最新的词云数据

  // 围绕robot.png生成位置（精确避开矩形区域）
  const getPositionAroundRobot = useMemo(() => {
    return (word: string, index: number, _totalWords: number) => {
      // robot.png位于中心 (50%, 50%)，尺寸120px
      const centerX = 50; // 百分比
      const centerY = 50; // 百分比

      // 假设词云容器宽度为视口宽度，高度为20vh
      // robot图片120px，转换为百分比（基于典型移动设备宽度375px和20vh≈150px）
      const robotWidthPercent = 32; // 120px / 375px ≈ 32%
      const robotHeightPercent = 80; // 120px / 150px = 80%

      // robot图片的边界（百分比）
      const robotLeft = centerX - robotWidthPercent / 2;   // ~34%
      const robotRight = centerX + robotWidthPercent / 2;  // ~66%
      const robotTop = centerY - robotHeightPercent / 2;   // ~10%
      const robotBottom = centerY + robotHeightPercent / 2; // ~90%

      // 生成基于词汇内容的稳定哈希
      const hash = word.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0);
        return a & a;
      }, 0);
      const seed = Math.abs(hash + index);

      let x, y;
      let attempts = 0;
      const maxAttempts = 50;

      do {
        // 使用黄金角度分布，确保均匀分布
        const goldenAngle = 137.5; // 黄金角度
        const angle = (index * goldenAngle + (seed % 60) + attempts * 7.3) % 360;

        // 分为四个主要区域：左、右、上、下
        if (angle >= 315 || angle < 45) {
          // 右侧区域
          x = robotRight + 5 + (seed % 25); // 71% - 96%
          y = 15 + (seed % 70); // 15% - 85%
        } else if (angle >= 45 && angle < 135) {
          // 上方区域
          x = 10 + (seed % 80); // 10% - 90%
          y = 5 + (seed % Math.max(5, robotTop - 5)); // 5% - robotTop
        } else if (angle >= 135 && angle < 225) {
          // 左侧区域
          x = 5 + (seed % Math.max(5, robotLeft - 10)); // 5% - (robotLeft-5)
          y = 15 + (seed % 70); // 15% - 85%
        } else {
          // 下方区域
          x = 10 + (seed % 80); // 10% - 90%
          y = Math.min(95, robotBottom + 5 + (seed % 10)); // robotBottom+5% - 95%
        }

        // 边界检查
        x = Math.max(5, Math.min(95, x));
        y = Math.max(5, Math.min(95, y));

        attempts++;
      } while (
        attempts < maxAttempts &&
        x >= robotLeft - 5 && x <= robotRight + 5 &&
        y >= robotTop - 5 && y <= robotBottom + 5
      );

      return {
        left: x,
        top: y,
        fontSize: (seed % 8) + 16, // 16px - 24px
        animationDelay: (seed % 10), // 0-10秒
        animationDuration: ((seed % 6) + 12) // 12-18秒
      };
    };
  }, []);

  // 组件始终显示（robot.png + 水波浪），只是词云文字条件渲染

  return (
    <div className="word-cloud-header">
      {/* 背景规整交织波浪线条 - 两组，每组8条，总共16条线 */}
      <div className="absolute inset-0 z-10">
        <WaveAnimation width={1200} height={200} lineCount={16} />
      </div>

      {/* 词云文字 - 围绕robot.png分布 */}
      {accumulatedWords.map((item, index) => {
        const position = getPositionAroundRobot(item.word, index, accumulatedWords.length);
        return (
          <div
            key={item.id}
            className="word-cloud-header-text"
            style={{
              left: `${position.left}%`,
              top: `${position.top}%`,
              fontSize: `${position.fontSize}px`,
              animationDelay: `${position.animationDelay}s`,
              animationDuration: `${position.animationDuration}s`,
              // 新添加的词汇有淡入效果
              animation: `headerFloatVertical ${position.animationDuration}s ease-in-out infinite ${position.animationDelay}s, fadeIn 0.5s ease-in-out`
            }}
          >
            {item.word}
          </div>
        );
      })}

      {/* 机器人头像 - 始终显示，居中，在波浪线之上 */}
      <div className="relative z-40">
        <SpineRobot
          className="robot-avatar-header"
          animation={currentAnimationType}
          loop={true}
          timeScale={1.0}
          onError={(error) => {
            console.warn('Spine animation error in WordCloudHeader:', error);
          }}
          onLoad={() => {
            console.log(`Spine animation loaded in WordCloudHeader: ${currentAnimationType}`);
          }}
        />
      </div>
    </div>
  );
});

WordCloudHeader.displayName = 'WordCloudHeader';

export default WordCloudHeader;
