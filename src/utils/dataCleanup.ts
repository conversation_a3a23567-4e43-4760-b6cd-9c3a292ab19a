/**
 * 数据清理工具函数
 * 用于清除localStorage中的各种数据
 */

import { clearToken } from './jwtUtils';

/**
 * 清除所有聊天相关记录
 * 包括会话列表、消息记录、推荐记录等
 */
export const clearAllChatRecords = (): void => {
  try {
    // 获取所有localStorage的键
    const keys = Object.keys(localStorage);
    
    // 需要清除的键名模式
    const keysToRemove = keys.filter(key => 
      key.startsWith('chat_car_recommendations_') ||
      key.startsWith('chat_conversation_messages_') ||
      key === 'chat_conversations' ||
      key === 'current_conversation_id' ||
      key === 'chat_user_id'
    );
    
    // 逐个删除
    keysToRemove.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`已清除 ${keysToRemove.length} 个聊天相关记录`);
  } catch (error) {
    console.error('清除聊天记录失败:', error);
    throw new Error('清除聊天记录失败');
  }
};

/**
 * 账号登出
 * 清除JWT token
 */
export const logout = (): void => {
  try {
    clearToken();
    console.log('已清除JWT token');
  } catch (error) {
    console.error('登出失败:', error);
    throw new Error('登出失败');
  }
};

/**
 * 完全重置应用数据
 * 清除所有用户数据（聊天记录 + JWT token）
 */
export const resetAllData = (): void => {
  try {
    clearAllChatRecords();
    logout();
    console.log('已重置所有应用数据');
  } catch (error) {
    console.error('重置应用数据失败:', error);
    throw new Error('重置应用数据失败');
  }
};

/**
 * 获取聊天记录统计信息
 * 用于显示清除前的数据量
 */
export const getChatRecordsStats = (): {
  conversationCount: number;
  messageCount: number;
  recommendationCount: number;
} => {
  try {
    const keys = Object.keys(localStorage);
    
    // 统计各类数据
    // const conversationKeys = keys.filter(key => key === 'chat_conversations'); // 暂时注释
    const messageKeys = keys.filter(key => key.startsWith('chat_conversation_messages_'));
    const recommendationKeys = keys.filter(key => key.startsWith('chat_car_recommendations_'));
    
    // 获取会话数量
    let conversationCount = 0;
    try {
      const conversations = localStorage.getItem('chat_conversations');
      if (conversations) {
        const parsed = JSON.parse(conversations);
        conversationCount = Array.isArray(parsed) ? parsed.length : 0;
      }
    } catch (error) {
      console.warn('解析会话数据失败:', error);
    }
    
    return {
      conversationCount,
      messageCount: messageKeys.length,
      recommendationCount: recommendationKeys.length
    };
  } catch (error) {
    console.error('获取聊天记录统计失败:', error);
    return {
      conversationCount: 0,
      messageCount: 0,
      recommendationCount: 0
    };
  }
};
