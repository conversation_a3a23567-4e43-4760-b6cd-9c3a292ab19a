/**
 * 头像处理工具函数
 * 包含图片压缩、base64转换、localStorage操作等功能
 */

// localStorage键名
const AVATAR_STORAGE_KEY = 'user_avatar_base64';

/**
 * 压缩图片到指定大小
 * @param file 原始图片文件
 * @param maxWidth 最大宽度，默认800px
 * @param maxHeight 最大高度，默认800px
 * @param quality 压缩质量，默认0.8
 * @returns Promise<string> base64格式的压缩图片
 */
export const compressImage = (
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 800,
  quality: number = 0.8
): Promise<string> => {
  return new Promise((resolve, reject) => {
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      reject(new Error('请选择图片文件'));
      return;
    }

    // 检查文件大小（10MB限制）
    if (file.size > 10 * 1024 * 1024) {
      reject(new Error('图片文件过大，请选择小于10MB的图片'));
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // 计算压缩后的尺寸
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = Math.floor(width * ratio);
          height = Math.floor(height * ratio);
        }

        // 设置canvas尺寸
        canvas.width = width;
        canvas.height = height;

        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height);

        // 转换为base64
        const compressedBase64 = canvas.toDataURL('image/jpeg', quality);
        
        // 检查压缩后的大小（约500KB限制）
        if (compressedBase64.length > 700000) {
          // 如果还是太大，进一步压缩
          const furtherCompressed = canvas.toDataURL('image/jpeg', 0.6);
          resolve(furtherCompressed);
        } else {
          resolve(compressedBase64);
        }
      } catch (error) {
        reject(new Error('图片压缩失败'));
      }
    };

    img.onerror = () => {
      reject(new Error('图片加载失败'));
    };

    // 创建图片URL
    img.src = URL.createObjectURL(file);
  });
};

/**
 * 将文件转换为base64格式
 * @param file 图片文件
 * @returns Promise<string> base64格式的图片
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('文件读取失败'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };
    
    reader.readAsDataURL(file);
  });
};

/**
 * 保存头像到localStorage
 * @param base64Image base64格式的图片
 */
export const saveAvatar = (base64Image: string): void => {
  try {
    localStorage.setItem(AVATAR_STORAGE_KEY, base64Image);
  } catch (error) {
    console.error('保存头像失败:', error);
    throw new Error('保存头像失败，可能是存储空间不足');
  }
};

/**
 * 从localStorage获取头像
 * @returns string | null base64格式的头像，如果没有则返回null
 */
export const getAvatar = (): string | null => {
  try {
    return localStorage.getItem(AVATAR_STORAGE_KEY);
  } catch (error) {
    console.error('获取头像失败:', error);
    return null;
  }
};

/**
 * 删除保存的头像
 */
export const removeAvatar = (): void => {
  try {
    localStorage.removeItem(AVATAR_STORAGE_KEY);
  } catch (error) {
    console.error('删除头像失败:', error);
  }
};

/**
 * 处理图片文件（压缩并保存）
 * @param file 图片文件
 * @returns Promise<string> 处理后的base64图片
 */
export const processAndSaveAvatar = async (file: File): Promise<string> => {
  try {
    // 压缩图片
    const compressedImage = await compressImage(file);
    
    // 保存到localStorage
    saveAvatar(compressedImage);
    
    return compressedImage;
  } catch (error) {
    console.error('处理头像失败:', error);
    throw error;
  }
};

/**
 * 检查浏览器是否支持相机功能
 * @returns boolean 是否支持相机
 */
export const isCameraSupported = (): boolean => {
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
};

/**
 * 检查是否为移动设备
 * @returns boolean 是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 获取文件大小的友好显示
 * @param bytes 字节数
 * @returns string 友好的文件大小显示
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 验证图片文件
 * @param file 文件对象
 * @returns boolean 是否为有效的图片文件
 */
export const validateImageFile = (file: File): { valid: boolean; error?: string } => {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: '请选择图片文件' };
  }
  
  // 检查文件大小（10MB限制）
  if (file.size > 10 * 1024 * 1024) {
    return { valid: false, error: '图片文件过大，请选择小于10MB的图片' };
  }
  
  // 检查支持的图片格式
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  if (!supportedTypes.includes(file.type)) {
    return { valid: false, error: '不支持的图片格式，请选择 JPEG、PNG、GIF 或 WebP 格式的图片' };
  }
  
  return { valid: true };
};
