/**
 * 认证相关工具函数
 * 整合设备检测和JWT验证逻辑
 */

import { isMobileDevice } from './deviceDetection';
import { isCurrentTokenValid, clearToken, getToken } from './jwtUtils';
import { buildAuthApiUrl, buildChatApiUrl, AUTH_ENDPOINTS, CHAT_ENDPOINTS } from './apiConfig';

/**
 * 认证状态枚举
 */
export enum AuthStatus {
  LOADING = 'loading',
  PC_NOT_SUPPORTED = 'pc_not_supported',
  NEED_LOGIN = 'need_login',
  AUTHENTICATED = 'authenticated'
}

/**
 * 检查用户认证状态
 * 综合考虑设备类型和JWT状态
 */
export const checkAuthStatus = (): AuthStatus => {
  try {
    // 首先检查是否为移动设备
    if (!isMobileDevice()) {
      return AuthStatus.PC_NOT_SUPPORTED;
    }
    
    // 检查JWT令牌状态
    if (!isCurrentTokenValid()) {
      // 清除无效令牌
      clearToken();
      return AuthStatus.NEED_LOGIN;
    }
    
    return AuthStatus.AUTHENTICATED;
  } catch (error) {
    console.error('检查认证状态失败:', error);
    return AuthStatus.NEED_LOGIN;
  }
};

/**
 * 手机号验证（根据不同国家区号）
 */
export const validatePhoneNumber = (phone: string, dialCode: string): { isValid: boolean; message?: string } => {
  if (!phone.trim()) {
    return { isValid: false, message: '请输入手机号' };
  }

  // 根据不同区号进行验证
  switch (dialCode) {
    case '+86': // 中国大陆
      if (!/^1[3-9]\d{9}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的中国大陆手机号' };
      }
      break;
    case '+852': // 香港
      if (!/^[5-9]\d{7}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的中国香港手机号' };
      }
      break;
    case '+886': // 台湾
      if (!/^9\d{8}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的中国台湾手机号' };
      }
      break;
    case '+1': // 美国/加拿大
      if (!/^\d{10}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的美国/加拿大手机号' };
      }
      break;
    default: // 其他国家，基本长度验证
      if (!/^\d{7,15}$/.test(phone)) {
        return { isValid: false, message: '请输入正确的手机号' };
      }
  }

  return { isValid: true };
};

/**
 * 验证码验证（严格6位数字）
 */
export const validateVerificationCode = (code: string): { isValid: boolean; message?: string } => {
  if (!code.trim()) {
    return { isValid: false, message: '请输入验证码' };
  }

  if (!/^\d{6}$/.test(code)) {
    return { isValid: false, message: '验证码必须为6位数字' };
  }

  return { isValid: true };
};

/**
 * 格式化手机号显示
 * 例：13812345678 -> 138 1234 5678
 */
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 11) {
    return `${cleaned.slice(0, 3)} ${cleaned.slice(3, 7)} ${cleaned.slice(7)}`;
  }
  return phone;
};

/**
 * 模拟登录API调用
 * 实际项目中应该调用真实的后端API
 */
export const mockLogin = async (phone: string, code: string, dialCode: string): Promise<{
  success: boolean;
  token?: string;
  message?: string;
}> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 验证手机号
  const phoneValidation = validatePhoneNumber(phone, dialCode);
  if (!phoneValidation.isValid) {
    return {
      success: false,
      message: phoneValidation.message
    };
  }

  // 验证验证码
  const codeValidation = validateVerificationCode(code);
  if (!codeValidation.isValid) {
    return {
      success: false,
      message: codeValidation.message
    };
  }

  // 模拟成功登录，返回JWT令牌
  // 实际项目中这个令牌应该由后端生成
  const mockToken = generateMockJWT(`${dialCode}${phone}`);

  return {
    success: true,
    token: mockToken,
    message: '登录成功'
  };
};

/**
 * 生成模拟JWT令牌（仅用于演示）
 * 实际项目中令牌应该由后端生成
 */
const generateMockJWT = (phone: string): string => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: phone,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60 // 24小时后过期
  }));
  const signature = 'mock_signature';
  
  return `${header}.${payload}.${signature}`;
};

/**
 * 发送验证码API响应接口
 */
interface SendCodeResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    expires_in: number;
    code: string;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 登录API响应接口
 */
interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    success: boolean;
    message: string;
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
    device_id: string;
    user_info: {
      id: number;
      uuid: string;
      phone: string;
      username: string;
      avatar_url: string;
    };
  };
  timestamp: string;
  request_id: string;
}

/**
 * 真实发送验证码API
 */
export const sendVerificationCodeAPI = async (phone: string): Promise<{
  success: boolean;
  message?: string;
  code?: string;
}> => {
  try {
    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.SEND_CODE), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: phone,
        purpose: 'login'
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: SendCodeResponse = await response.json();

    if (data.success && data.data.success) {
      return {
        success: true,
        message: data.data.message,
        code: data.data.code // 返回验证码用于自动填充
      };
    } else {
      return {
        success: false,
        message: data.message || '发送验证码失败'
      };
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 真实登录API
 */
export const loginWithCodeAPI = async (phone: string, code: string): Promise<{
  success: boolean;
  message?: string;
  loginData?: any;
}> => {
  try {
    const response = await fetch(buildAuthApiUrl(AUTH_ENDPOINTS.LOGIN), {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        phone: phone,
        code: code
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: LoginResponse = await response.json();

    if (data.success && data.data.success) {
      // 排除user_info，只返回其他数据用于存储
      const { user_info, ...loginDataWithoutUserInfo } = data.data;

      return {
        success: true,
        message: data.data.message,
        loginData: loginDataWithoutUserInfo
      };
    } else {
      return {
        success: false,
        message: data.message || '登录失败'
      };
    }
  } catch (error) {
    console.error('登录失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 模拟发送验证码API（保留用于兼容性）
 */
export const mockSendVerificationCode = async (phone: string, dialCode: string): Promise<{
  success: boolean;
  message?: string;
}> => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  const phoneValidation = validatePhoneNumber(phone, dialCode);
  if (!phoneValidation.isValid) {
    return {
      success: false,
      message: phoneValidation.message
    };
  }

  return {
    success: true,
    message: '验证码已发送'
  };
};

/**
 * 创建会话API响应接口
 */
interface CreateConversationResponse {
  success: boolean;
  message: string;
  data: {
    id: number;
    conversation_id: string;
    user_id: number;
    title: string;
    is_deleted: boolean;
    created_at: string;
    updated_at: string | null;
    deleted_at: string | null;
  };
  timestamp: string;
  request_id: string;
}

/**
 * 创建新会话API
 * @param title 会话标题，默认为"新的会话"
 * @returns Promise<{success: boolean, conversationId?: string, message?: string}>
 */
export const createConversationAPI = async (title: string = '新的会话'): Promise<{
  success: boolean;
  conversationId?: string;
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.CREATE_CONVERSATION), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({ title }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: CreateConversationResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        conversationId: data.data.conversation_id,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '创建会话失败'
      };
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};

/**
 * 获取会话列表API响应接口
 */
interface GetConversationsResponse {
  success: boolean;
  message: string;
  data: Array<{
    id: number;
    conversation_id: string;
    title: string;
    created_at: string; // ISO字符串
    updated_at: string | null; // ISO字符串或null
  }>;
  timestamp: string;
  request_id: string;
}

/**
 * 会话数据接口（API格式）
 */
export interface APIConversation {
  id: number;
  conversation_id: string;
  title: string;
  created_at: string;
  updated_at: string | null;
}

/**
 * 获取会话列表API
 * @returns Promise<{success: boolean, conversations?: APIConversation[], message?: string}>
 */
export const getConversationsAPI = async (): Promise<{
  success: boolean;
  conversations?: APIConversation[];
  message?: string;
}> => {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('未找到认证令牌，请重新登录');
    }

    const response = await fetch(buildChatApiUrl(CHAT_ENDPOINTS.CREATE_CONVERSATION), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: GetConversationsResponse = await response.json();

    if (data.success) {
      return {
        success: true,
        conversations: data.data,
        message: data.message
      };
    } else {
      return {
        success: false,
        message: data.message || '获取会话列表失败'
      };
    }
  } catch (error) {
    console.error('获取会话列表失败:', error);
    return {
      success: false,
      message: '网络错误，请重试'
    };
  }
};
