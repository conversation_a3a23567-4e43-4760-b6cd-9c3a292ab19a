/**
 * API配置文件
 * 统一管理所有API的基础配置
 */

// API基础URL配置
export const API_CONFIG = {
  // 认证API基础URL - 可以通过环境变量或配置文件修改
  AUTH_BASE_URL: '/auth-api', // 代理到 http://0.0.0.0:8000
  
  // SenseTime API基础URL
  SENSETIME_BASE_URL: '/api', // 代理到 https://api-gai.metishon.co
  
  // TTS API基础URL
  TTS_BASE_URL: '/tts-api', // 代理到 https://openspeech.bytedance.com
  
  // 超时配置
  TIMEOUT: 30000, // 30秒
};

// 认证API端点
export const AUTH_ENDPOINTS = {
  SEND_CODE: '/api/v1/auth/send-code',
  LOGIN: '/api/v1/auth/login',
  REFRESH: '/api/v1/auth/refresh',
  LOGOUT: '/api/v1/auth/logout',
};

// 聊天API端点（使用与认证API相同的基础URL）
export const CHAT_ENDPOINTS = {
  COMPLETIONS: '/api/v1/chat/completions',
  CREATE_CONVERSATION: '/api/v1/conversations/', // 创建新会话
  SUMMARY_TOPICS: '/api/v1/chat/summary_topics', // 新的话题总结端点
  TTS: '/api/v1/tts/', // 新的TTS端点
  ASR: '/api/v1/car/asr', // 新的ASR端点
  RECOMMEND_TOPICS: '/car/recommend_topics', // 保持原有端点
  VEHICLE_PICTURES: '/api/v1/car/vehicle_cars', // 新的车辆图片端点
  VEHICLE_DAMAGE: '/api/v1/car/vehicle_cars', // 新的车辆损伤端点
};

// 构建完整的API URL
export const buildAuthApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.AUTH_BASE_URL}${endpoint}`;
};

// 构建聊天API URL（复用认证API的基础URL）
export const buildChatApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.AUTH_BASE_URL}${endpoint}`;
};

/**
 * 获取当前认证API的完整基础URL（用于调试）
 */
export const getAuthApiBaseUrl = (): string => {
  return API_CONFIG.AUTH_BASE_URL;
};

/**
 * 更新认证API基础URL（用于动态切换）
 * 注意：这只会影响后续的API调用，不会更新Vite代理配置
 */
export const updateAuthApiBaseUrl = (newBaseUrl: string): void => {
  API_CONFIG.AUTH_BASE_URL = newBaseUrl;
  console.log('认证API基础URL已更新为:', newBaseUrl);
};
