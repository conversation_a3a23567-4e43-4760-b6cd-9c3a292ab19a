/**
 * 会话存储管理工具
 * 管理聊天会话的本地存储，包括会话列表、当前会话等
 */

import { generateUUID } from './chatApi';
import type { APIConversation } from './auth';

// 存储的消息数据结构
export interface StoredMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  carRecommendations?: any[]; // 车辆推荐数据
  activeTaskType?: string; // 任务类型
  slots?: Record<string, any>; // slots数据
}

// 推荐记录数据结构
export interface CarRecommendationRecord {
  id: string; // 推荐记录的唯一ID
  messageId: string; // 关联的消息ID
  conversationId: string; // 关联的会话ID
  recommendations: any[]; // 推荐的车辆数据
  activeTaskType?: string; // 任务类型
  timestamp: number; // 创建时间
}

// 会话数据结构
export interface Conversation {
  id: string;
  title: string;
  created_at: number;
  updated_at: number;
  message_count: number;
}

// 带消息的完整会话数据结构
export interface ConversationWithMessages extends Conversation {
  messages: StoredMessage[];
}

// 本地存储键名
const STORAGE_KEYS = {
  CONVERSATIONS: 'chat_conversations',
  CURRENT_CONVERSATION_ID: 'current_conversation_id',
  USER_ID: 'chat_user_id',
  CONVERSATION_MESSAGES: 'chat_conversation_messages', // 消息存储键前缀
  CAR_RECOMMENDATIONS: 'chat_car_recommendations' // 推荐记录存储键前缀
};

/**
 * 获取所有会话列表
 * @returns Conversation[] 会话列表
 */
export const getConversations = (): Conversation[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.CONVERSATIONS);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取会话列表失败:', error);
    return [];
  }
};

/**
 * 保存会话列表
 * @param conversations 会话列表
 */
export const saveConversations = (conversations: Conversation[]): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.CONVERSATIONS, JSON.stringify(conversations));
  } catch (error) {
    console.error('保存会话列表失败:', error);
  }
};

/**
 * 创建新会话（使用真实API）
 * @param title 会话标题（可选，默认为"新的会话"）
 * @returns Promise<string> 新会话的ID
 */
export const createNewConversation = async (title: string = '新的会话'): Promise<string> => {
  try {
    // 动态导入以避免循环依赖
    const { createConversationAPI } = await import('./auth');

    // 调用真实API创建会话
    const result = await createConversationAPI(title);

    if (result.success && result.conversationId) {
      const conversationId = result.conversationId;
      const now = Date.now();

      const newConversation: Conversation = {
        id: conversationId,
        title,
        created_at: now,
        updated_at: now,
        message_count: 0
      };

      const conversations = getConversations();
      conversations.unshift(newConversation); // 添加到列表开头
      saveConversations(conversations);

      // 设置为当前会话
      setCurrentConversationId(conversationId);

      console.log('✅ 会话创建成功，API返回ID:', conversationId);
      return conversationId;
    } else {
      throw new Error(result.message || '创建会话失败');
    }
  } catch (error) {
    console.error('❌ API创建会话失败，使用本地生成:', error);

    // API失败时的降级处理：使用本地生成的UUID
    const conversationId = generateUUID();
    const now = Date.now();

    const newConversation: Conversation = {
      id: conversationId,
      title,
      created_at: now,
      updated_at: now,
      message_count: 0
    };

    const conversations = getConversations();
    conversations.unshift(newConversation);
    saveConversations(conversations);

    setCurrentConversationId(conversationId);

    console.log('⚠️ 使用本地生成的会话ID:', conversationId);
    return conversationId;
  }
};

/**
 * 更新会话信息
 * @param conversationId 会话ID
 * @param updates 要更新的字段
 */
export const updateConversation = (
  conversationId: string, 
  updates: Partial<Pick<Conversation, 'title' | 'message_count'>>
): void => {
  const conversations = getConversations();
  const index = conversations.findIndex(conv => conv.id === conversationId);
  
  if (index !== -1) {
    conversations[index] = {
      ...conversations[index],
      ...updates,
      updated_at: Date.now()
    };
    saveConversations(conversations);
  }
};

/**
 * 删除整个会话（包括会话信息和所有消息）
 * @param conversationId 会话ID
 */
export const deleteConversation = (conversationId: string): void => {
  // 删除会话消息
  deleteConversationMessages(conversationId);

  // 从会话列表中移除
  const conversations = getConversations();
  const filteredConversations = conversations.filter(conv => conv.id !== conversationId);
  saveConversations(filteredConversations);

  // 如果删除的是当前会话，清除当前会话ID
  if (getCurrentConversationId() === conversationId) {
    clearCurrentConversationId();
  }
};

/**
 * 获取当前会话ID
 * @returns string | null 当前会话ID
 */
export const getCurrentConversationId = (): string | null => {
  return localStorage.getItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID);
};

/**
 * 设置当前会话ID
 * @param conversationId 会话ID
 */
export const setCurrentConversationId = (conversationId: string): void => {
  localStorage.setItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID, conversationId);
};

/**
 * 清除当前会话ID
 */
export const clearCurrentConversationId = (): void => {
  localStorage.removeItem(STORAGE_KEYS.CURRENT_CONVERSATION_ID);
};

/**
 * 获取指定会话的详细信息
 * @param conversationId 会话ID
 * @returns Conversation | null 会话信息
 */
export const getConversationById = (conversationId: string): Conversation | null => {
  const conversations = getConversations();
  return conversations.find(conv => conv.id === conversationId) || null;
};

/**
 * 开始新对话
 * 创建新会话并设置为当前会话
 * @returns Promise<string> 新会话ID
 */
export const startNewConversation = async (): Promise<string> => {
  return await createNewConversation();
};

/**
 * 过滤和转换API会话数据
 * @param apiConversations API返回的会话数据
 * @returns 过滤后的本地会话数据
 */
const filterAndConvertAPIConversations = (apiConversations: APIConversation[]): Conversation[] => {
  const now = Date.now();
  const tenMinutes = 10 * 60 * 1000; // 10分钟的毫秒数

  return apiConversations
    .filter(conv => {
      // 过滤条件：如果updated_at为null且created_at超过10分钟，则不显示
      if (conv.updated_at === null) {
        const createdTime = new Date(conv.created_at).getTime();
        const timeDiff = now - createdTime;
        if (timeDiff > tenMinutes) {
          return false; // 过滤掉
        }
      }
      return true; // 保留
    })
    .map(conv => ({
      id: conv.conversation_id,
      title: conv.title,
      created_at: new Date(conv.created_at).getTime(),
      updated_at: conv.updated_at ? new Date(conv.updated_at).getTime() : new Date(conv.created_at).getTime(),
      message_count: 0 // API暂时不返回消息数量，设为0
    }))
    .sort((a, b) => b.updated_at - a.updated_at); // 按updated_at降序排列
};

/**
 * 获取会话历史记录（从API获取，按时间分组）
 * @returns Promise<分组的会话历史>
 */
export const getGroupedConversations = async () => {
  try {
    // 动态导入以避免循环依赖
    const { getConversationsAPI } = await import('./auth');

    // 调用API获取会话列表
    const result = await getConversationsAPI();

    let conversations: Conversation[] = [];

    if (result.success && result.conversations) {
      // 使用API数据
      conversations = filterAndConvertAPIConversations(result.conversations);
      console.log('✅ 从API获取会话列表成功，数量:', conversations.length);

      // 更新本地缓存
      saveConversations(conversations);
    } else {
      // API失败时使用本地缓存
      console.warn('⚠️ API获取会话列表失败，使用本地缓存:', result.message);
      conversations = getConversations();
    }

    // 按时间分组
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;

    const groups = {
      today: [] as Conversation[],
      yesterday: [] as Conversation[],
      thisWeek: [] as Conversation[],
      older: [] as Conversation[]
    };

    conversations.forEach(conv => {
      const timeDiff = now - conv.updated_at;

      if (timeDiff < oneDay) {
        groups.today.push(conv);
      } else if (timeDiff < 2 * oneDay) {
        groups.yesterday.push(conv);
      } else if (timeDiff < oneWeek) {
        groups.thisWeek.push(conv);
      } else {
        groups.older.push(conv);
      }
    });

    return groups;
  } catch (error) {
    console.error('❌ 获取会话历史失败，使用本地缓存:', error);

    // 完全失败时使用本地缓存的原有逻辑
    const conversations = getConversations();
    const now = Date.now();
    const oneDay = 24 * 60 * 60 * 1000;
    const oneWeek = 7 * oneDay;

    const groups = {
      today: [] as Conversation[],
      yesterday: [] as Conversation[],
      thisWeek: [] as Conversation[],
      older: [] as Conversation[]
    };

    conversations.forEach(conv => {
      const timeDiff = now - conv.updated_at;

      if (timeDiff < oneDay) {
        groups.today.push(conv);
      } else if (timeDiff < 2 * oneDay) {
        groups.yesterday.push(conv);
      } else if (timeDiff < oneWeek) {
        groups.thisWeek.push(conv);
      } else {
        groups.older.push(conv);
      }
    });

    return groups;
  }
};

/**
 * 清理旧会话（保留最近的50个会话）
 */
export const cleanupOldConversations = (): void => {
  const conversations = getConversations();
  if (conversations.length > 50) {
    // 按更新时间排序，保留最新的50个
    const sortedConversations = [...conversations]
      .sort((a, b) => b.updated_at - a.updated_at);
    const conversationsToKeep = sortedConversations.slice(0, 50);
    const conversationsToDelete = sortedConversations.slice(50);

    // 删除旧会话的消息数据
    conversationsToDelete.forEach(conv => {
      cleanupConversationData(conv.id);
    });

    saveConversations(conversationsToKeep);
  }
};

/**
 * 增加会话消息计数
 * @param conversationId 会话ID
 */
export const incrementMessageCount = (conversationId: string): void => {
  const conversation = getConversationById(conversationId);
  if (conversation) {
    updateConversation(conversationId, {
      message_count: conversation.message_count + 1
    });
  }
};

// ==================== 消息存储功能 ====================

/**
 * 获取会话消息存储键名
 * @param conversationId 会话ID
 * @returns string 存储键名
 */
const getMessagesStorageKey = (conversationId: string): string => {
  return `${STORAGE_KEYS.CONVERSATION_MESSAGES}_${conversationId}`;
};

/**
 * 获取指定会话的所有消息
 * @param conversationId 会话ID
 * @returns StoredMessage[] 消息列表
 */
export const getConversationMessages = (conversationId: string): StoredMessage[] => {
  try {
    const storageKey = getMessagesStorageKey(conversationId);
    const stored = localStorage.getItem(storageKey);
    const messages = stored ? JSON.parse(stored) : [];

    // 去重逻辑：根据消息ID去除重复项
    const uniqueMessages = messages.filter((message: StoredMessage, index: number, array: StoredMessage[]) => {
      return array.findIndex(m => m.id === message.id) === index;
    });

    // 如果发现重复消息，更新存储
    if (uniqueMessages.length !== messages.length) {
      console.log(`发现并清理了 ${messages.length - uniqueMessages.length} 条重复消息`);
      saveConversationMessages(conversationId, uniqueMessages);
    }

    return uniqueMessages;
  } catch (error) {
    console.error('获取会话消息失败:', error);
    return [];
  }
};

/**
 * 保存会话消息
 * @param conversationId 会话ID
 * @param messages 消息列表
 */
export const saveConversationMessages = (conversationId: string, messages: StoredMessage[]): void => {
  try {
    const storageKey = getMessagesStorageKey(conversationId);
    localStorage.setItem(storageKey, JSON.stringify(messages));
  } catch (error) {
    console.error('保存会话消息失败:', error);
    // 如果存储失败，可能是容量不足，尝试清理旧数据
    cleanupOldConversations();
    try {
      const storageKey = getMessagesStorageKey(conversationId);
      localStorage.setItem(storageKey, JSON.stringify(messages));
    } catch (retryError) {
      console.error('重试保存会话消息仍然失败:', retryError);
    }
  }
};

/**
 * 添加消息到指定会话
 * @param conversationId 会话ID
 * @param message 要添加的消息
 */
export const addMessageToConversation = (conversationId: string, message: StoredMessage): void => {
  const messages = getConversationMessages(conversationId);

  // 检查是否已存在相同ID的消息
  const existingMessageIndex = messages.findIndex(m => m.id === message.id);
  if (existingMessageIndex !== -1) {
    console.log(`消息 ${message.id} 已存在，跳过添加`);
    return;
  }

  messages.push(message);
  saveConversationMessages(conversationId, messages);
};

/**
 * 更新会话中的指定消息
 * @param conversationId 会话ID
 * @param messageId 消息ID
 * @param updates 要更新的字段
 */
export const updateMessageInConversation = (
  conversationId: string,
  messageId: string,
  updates: Partial<StoredMessage>
): void => {
  const messages = getConversationMessages(conversationId);
  const messageIndex = messages.findIndex(msg => msg.id === messageId);

  if (messageIndex !== -1) {
    messages[messageIndex] = { ...messages[messageIndex], ...updates };
    saveConversationMessages(conversationId, messages);
  }
};

/**
 * 获取带消息的完整会话数据
 * @param conversationId 会话ID
 * @returns ConversationWithMessages | null 完整会话数据
 */
export const getConversationWithMessages = (conversationId: string): ConversationWithMessages | null => {
  const conversation = getConversationById(conversationId);
  if (!conversation) {
    return null;
  }

  const messages = getConversationMessages(conversationId);
  return {
    ...conversation,
    messages
  };
};

/**
 * 删除会话的所有消息
 * @param conversationId 会话ID
 */
export const deleteConversationMessages = (conversationId: string): void => {
  try {
    const storageKey = getMessagesStorageKey(conversationId);
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error('删除会话消息失败:', error);
  }
};



/**
 * 清理指定会话ID的消息数据（在删除会话时调用）
 * @param conversationId 会话ID
 */
export const cleanupConversationData = (conversationId: string): void => {
  deleteConversationMessages(conversationId);
  deleteConversationRecommendations(conversationId);
};

// ==================== 推荐记录存储功能 ====================

/**
 * 获取推荐记录存储键名
 * @param conversationId 会话ID
 * @returns string 存储键名
 */
const getRecommendationsStorageKey = (conversationId: string): string => {
  return `${STORAGE_KEYS.CAR_RECOMMENDATIONS}_${conversationId}`;
};

/**
 * 获取指定会话的所有推荐记录
 * @param conversationId 会话ID
 * @returns CarRecommendationRecord[] 推荐记录列表
 */
export const getConversationRecommendations = (conversationId: string): CarRecommendationRecord[] => {
  try {
    const storageKey = getRecommendationsStorageKey(conversationId);
    const stored = localStorage.getItem(storageKey);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('获取推荐记录失败:', error);
    return [];
  }
};

/**
 * 保存会话推荐记录
 * @param conversationId 会话ID
 * @param recommendations 推荐记录列表
 */
export const saveConversationRecommendations = (conversationId: string, recommendations: CarRecommendationRecord[]): void => {
  try {
    const storageKey = getRecommendationsStorageKey(conversationId);
    localStorage.setItem(storageKey, JSON.stringify(recommendations));
  } catch (error) {
    console.error('保存推荐记录失败:', error);
  }
};

/**
 * 添加推荐记录到指定会话
 * @param conversationId 会话ID
 * @param messageId 关联的消息ID
 * @param recommendations 推荐数据
 * @param activeTaskType 任务类型
 * @returns string 推荐记录ID
 */
export const addRecommendationToConversation = (
  conversationId: string,
  messageId: string,
  recommendations: any[],
  activeTaskType?: string
): string => {
  const recordId = generateUUID();
  const record: CarRecommendationRecord = {
    id: recordId,
    messageId,
    conversationId,
    recommendations,
    activeTaskType,
    timestamp: Date.now()
  };

  const existingRecords = getConversationRecommendations(conversationId);
  existingRecords.push(record);
  saveConversationRecommendations(conversationId, existingRecords);

  return recordId;
};

/**
 * 根据消息ID获取推荐记录
 * @param conversationId 会话ID
 * @param messageId 消息ID
 * @returns CarRecommendationRecord | null 推荐记录
 */
export const getRecommendationByMessageId = (conversationId: string, messageId: string): CarRecommendationRecord | null => {
  const recommendations = getConversationRecommendations(conversationId);
  return recommendations.find(rec => rec.messageId === messageId) || null;
};

/**
 * 删除会话的所有推荐记录
 * @param conversationId 会话ID
 */
export const deleteConversationRecommendations = (conversationId: string): void => {
  try {
    const storageKey = getRecommendationsStorageKey(conversationId);
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.error('删除推荐记录失败:', error);
  }
};

/**
 * 删除指定的推荐记录
 * @param conversationId 会话ID
 * @param recordId 推荐记录ID
 */
export const deleteRecommendationRecord = (conversationId: string, recordId: string): void => {
  const recommendations = getConversationRecommendations(conversationId);
  const filteredRecommendations = recommendations.filter(rec => rec.id !== recordId);
  saveConversationRecommendations(conversationId, filteredRecommendations);
};

/**
 * 获取所有会话的推荐记录
 * @returns CarRecommendationRecord[] 所有推荐记录列表，按时间倒序排列
 */
export const getAllCarRecommendations = (): CarRecommendationRecord[] => {
  const conversations = getConversations();
  const allRecommendations: CarRecommendationRecord[] = [];

  conversations.forEach(conversation => {
    const conversationRecommendations = getConversationRecommendations(conversation.id);
    allRecommendations.push(...conversationRecommendations);
  });

  // 按时间倒序排列（最新的在前）
  return allRecommendations.sort((a, b) => b.timestamp - a.timestamp);
};

/**
 * 按日期分组获取所有推荐记录
 * @returns Record<string, CarRecommendationRecord[]> 按日期分组的推荐记录
 */
export const getGroupedCarRecommendations = (): Record<string, CarRecommendationRecord[]> => {
  const allRecommendations = getAllCarRecommendations();
  const grouped: Record<string, CarRecommendationRecord[]> = {};

  allRecommendations.forEach(record => {
    const date = new Date(record.timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const threeDaysAgo = new Date(today);
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    let dateKey: string;

    if (date.toDateString() === today.toDateString()) {
      dateKey = '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      dateKey = '昨天';
    } else if (date.toDateString() === threeDaysAgo.toDateString()) {
      dateKey = '三天前';
    } else {
      // 格式化为 "7月15日" 的形式
      dateKey = `${date.getMonth() + 1}月${date.getDate()}日`;
    }

    if (!grouped[dateKey]) {
      grouped[dateKey] = [];
    }
    grouped[dateKey].push(record);
  });

  return grouped;
};
