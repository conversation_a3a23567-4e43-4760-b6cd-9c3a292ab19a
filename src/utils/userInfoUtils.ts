/**
 * 用户信息存储工具函数
 * 管理昵称、电话等用户信息的localStorage操作
 */

// localStorage键名
const USER_INFO_KEYS = {
  NICKNAME: 'user_nickname',
  PHONE: 'user_phone',
  CITY: 'user_city',
  ROBOT_AVATAR: 'robot_avatar',
  LANGUAGE: 'user_language'
};

/**
 * 保存用户昵称
 * @param nickname 用户昵称
 */
export const saveNickname = (nickname: string): void => {
  try {
    localStorage.setItem(USER_INFO_KEYS.NICKNAME, nickname.trim());
  } catch (error) {
    console.error('保存昵称失败:', error);
    throw new Error('保存昵称失败');
  }
};

/**
 * 获取用户昵称
 * @returns string 用户昵称，默认为"一个普通的用户"
 */
export const getNickname = (): string => {
  try {
    return localStorage.getItem(USER_INFO_KEYS.NICKNAME) || '一个普通的用户';
  } catch (error) {
    console.error('获取昵称失败:', error);
    return '一个普通的用户';
  }
};

/**
 * 保存用户电话
 * @param phone 用户电话
 */
export const savePhone = (phone: string): void => {
  try {
    localStorage.setItem(USER_INFO_KEYS.PHONE, phone.trim());
  } catch (error) {
    console.error('保存电话失败:', error);
    throw new Error('保存电话失败');
  }
};

/**
 * 获取用户电话
 * @returns string 用户电话，默认为"13999999999"
 */
export const getPhone = (): string => {
  try {
    return localStorage.getItem(USER_INFO_KEYS.PHONE) || '13999999999';
  } catch (error) {
    console.error('获取电话失败:', error);
    return '13999999999';
  }
};

/**
 * 保存用户城市
 * @param city 用户城市
 */
export const saveCity = (city: string): void => {
  try {
    localStorage.setItem(USER_INFO_KEYS.CITY, city.trim());
  } catch (error) {
    console.error('保存城市失败:', error);
    throw new Error('保存城市失败');
  }
};

/**
 * 获取用户城市
 * @returns string 用户城市，默认为"北京市"
 */
export const getCity = (): string => {
  try {
    return localStorage.getItem(USER_INFO_KEYS.CITY) || '北京市';
  } catch (error) {
    console.error('获取城市失败:', error);
    return '北京市';
  }
};

/**
 * 验证昵称
 * @param nickname 昵称
 * @returns 验证结果
 */
export const validateNickname = (nickname: string): { valid: boolean; error?: string } => {
  const trimmed = nickname.trim();
  
  if (!trimmed) {
    return { valid: false, error: '昵称不能为空' };
  }
  
  if (trimmed.length < 1) {
    return { valid: false, error: '昵称至少需要1个字符' };
  }
  
  if (trimmed.length > 20) {
    return { valid: false, error: '昵称不能超过20个字符' };
  }
  
  // 检查是否包含特殊字符
  const invalidChars = /[<>\"'&]/;
  if (invalidChars.test(trimmed)) {
    return { valid: false, error: '昵称不能包含特殊字符' };
  }
  
  return { valid: true };
};

/**
 * 验证电话号码
 * @param phone 电话号码
 * @returns 验证结果
 */
export const validatePhone = (phone: string): { valid: boolean; error?: string } => {
  const trimmed = phone.trim();
  
  if (!trimmed) {
    return { valid: false, error: '电话号码不能为空' };
  }
  
  // 移除所有非数字字符进行验证
  const numbersOnly = trimmed.replace(/\D/g, '');
  
  // 中国手机号验证（11位，1开头）
  const chineseMobileRegex = /^1[3-9]\d{9}$/;
  if (chineseMobileRegex.test(numbersOnly)) {
    return { valid: true };
  }
  
  // 固定电话验证（区号+号码，7-11位）
  const landlineRegex = /^\d{7,11}$/;
  if (landlineRegex.test(numbersOnly)) {
    return { valid: true };
  }
  
  // 国际号码验证（允许+号开头，6-15位数字）
  const internationalRegex = /^\+?\d{6,15}$/;
  if (internationalRegex.test(trimmed)) {
    return { valid: true };
  }
  
  return { valid: false, error: '请输入有效的电话号码' };
};

/**
 * 格式化电话号码显示
 * @param phone 原始电话号码
 * @returns 格式化后的电话号码
 */
export const formatPhone = (phone: string): string => {
  const trimmed = phone.trim();
  const numbersOnly = trimmed.replace(/\D/g, '');
  
  // 中国手机号格式化：138 1234 5678
  if (/^1[3-9]\d{9}$/.test(numbersOnly)) {
    return numbersOnly.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3');
  }
  
  // 其他情况返回原始输入
  return trimmed;
};

/**
 * 清除所有用户信息
 */
export const clearAllUserInfo = (): void => {
  try {
    Object.values(USER_INFO_KEYS).forEach(key => {
      localStorage.removeItem(key);
    });
  } catch (error) {
    console.error('清除用户信息失败:', error);
  }
};

/**
 * 保存机器人头像
 * @param avatarId 机器人头像ID
 */
export const saveRobotAvatar = (avatarId: string): void => {
  try {
    localStorage.setItem(USER_INFO_KEYS.ROBOT_AVATAR, avatarId.trim());
  } catch (error) {
    console.error('保存机器人头像失败:', error);
    throw new Error('保存机器人头像失败');
  }
};

/**
 * 获取机器人头像
 * @returns string 机器人头像ID，默认为"blue"
 */
export const getRobotAvatar = (): string => {
  try {
    return localStorage.getItem(USER_INFO_KEYS.ROBOT_AVATAR) || 'blue';
  } catch (error) {
    console.error('获取机器人头像失败:', error);
    return 'blue';
  }
};

/**
 * 保存用户语言
 * @param languageCode 语言代码
 */
export const saveLanguage = (languageCode: string): void => {
  try {
    localStorage.setItem(USER_INFO_KEYS.LANGUAGE, languageCode.trim());
  } catch (error) {
    console.error('保存语言失败:', error);
    throw new Error('保存语言失败');
  }
};

/**
 * 获取用户语言
 * @returns string 语言代码，默认为"zh-CN"
 */
export const getLanguage = (): string => {
  try {
    return localStorage.getItem(USER_INFO_KEYS.LANGUAGE) || 'zh-CN';
  } catch (error) {
    console.error('获取语言失败:', error);
    return 'zh-CN';
  }
};

/**
 * 获取所有用户信息
 * @returns 用户信息对象
 */
export const getAllUserInfo = () => {
  return {
    nickname: getNickname(),
    phone: getPhone(),
    city: getCity(),
    robotAvatar: getRobotAvatar(),
    language: getLanguage()
  };
};
