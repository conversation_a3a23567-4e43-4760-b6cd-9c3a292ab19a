/**
 * 定位工具函数
 * 包含IP定位API调用和城市匹配功能
 */

import { chinaAreaData, Province, City } from '../data/chinaAreaData';

// IP定位API响应接口
export interface LocationResponse {
  status: string;
  continent: string;
  continentCode: string;
  country: string;
  countryCode: string;
  region: string;
  regionName: string;
  city: string;
  district: string;
  zip: string;
  lat: number;
  lon: number;
  timezone: string;
  offset: number;
  currency: string;
  isp: string;
  org: string;
  as: string;
  asname: string;
  mobile: boolean;
  proxy: boolean;
  hosting: boolean;
  query: string;
}

// 城市匹配结果接口
export interface CityMatchResult {
  province: Province;
  city: City;
}

/**
 * 调用IP定位API获取当前位置
 * @returns Promise<LocationResponse> 定位结果
 */
export const getLocationByIP = async (): Promise<LocationResponse> => {
  try {
    // 尝试使用原始API
    const response = await fetch('http://demo.ip-api.com/json/?fields=66842623&lang=zh-CN', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
      // 添加超时控制
      signal: AbortSignal.timeout(10000), // 10秒超时
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.status !== 'success') {
      throw new Error('定位服务返回失败状态');
    }

    return data;
  } catch (error) {
    console.error('IP定位失败:', error);

    // 如果是跨域或网络问题，返回模拟数据用于测试
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.warn('使用模拟定位数据');
      return {
        status: 'success',
        continent: '亚洲',
        continentCode: 'AS',
        country: '中国',
        countryCode: 'CN',
        region: 'BJ',
        regionName: '北京市',
        city: '北京',
        district: '',
        zip: '100000',
        lat: 39.9042,
        lon: 116.4074,
        timezone: 'Asia/Shanghai',
        offset: 28800,
        currency: 'CNY',
        isp: 'Test ISP',
        org: '',
        as: 'AS Test',
        asname: 'TEST-AS',
        mobile: false,
        proxy: false,
        hosting: false,
        query: '127.0.0.1'
      };
    }

    throw new Error('定位失败，请检查网络连接');
  }
};

/**
 * 根据城市名称在省市数据中查找匹配项
 * @param cityName 城市名称
 * @returns CityMatchResult | null 匹配结果
 */
export const findCityByName = (cityName: string): CityMatchResult | null => {
  if (!cityName) return null;

  // 清理城市名称，移除常见后缀
  const cleanCityName = cityName
    .replace(/市$/, '')
    .replace(/县$/, '')
    .replace(/区$/, '')
    .replace(/特别行政区$/, '')
    .trim();

  // 遍历所有省份和城市进行匹配
  for (const province of chinaAreaData) {
    for (const city of province.cities) {
      const cleanDbCityName = city.name
        .replace(/市$/, '')
        .replace(/县$/, '')
        .replace(/区$/, '')
        .trim();

      // 精确匹配
      if (cleanDbCityName === cleanCityName) {
        return { province, city };
      }

      // 包含匹配（处理如"香港"匹配"香港特别行政区"的情况）
      if (cleanDbCityName.includes(cleanCityName) || cleanCityName.includes(cleanDbCityName)) {
        return { province, city };
      }
    }
  }

  return null;
};

/**
 * 根据国家和城市信息匹配中国省市
 * @param country 国家名称
 * @param city 城市名称
 * @returns CityMatchResult | null 匹配结果
 */
export const matchChineseCity = (country: string, city: string): CityMatchResult | null => {
  // 特殊地区处理
  const specialRegions: { [key: string]: string } = {
    '香港': '香港',
    'Hong Kong': '香港',
    '澳门': '澳门',
    'Macao': '澳门',
    'Macau': '澳门',
    '台湾': '台北市',
    'Taiwan': '台北市',
  };

  // 检查特殊地区
  if (specialRegions[country]) {
    return findCityByName(specialRegions[country]);
  }

  if (specialRegions[city]) {
    return findCityByName(specialRegions[city]);
  }

  // 如果是中国，直接匹配城市
  if (country === '中国' || country === 'China' || country === 'CN') {
    return findCityByName(city);
  }

  // 其他情况返回null，使用默认城市
  return null;
};

/**
 * 获取默认城市（北京市）
 * @returns CityMatchResult 默认城市
 */
export const getDefaultCity = (): CityMatchResult => {
  const beijing = findCityByName('北京市');
  if (beijing) {
    return beijing;
  }

  // 如果找不到北京，返回第一个城市
  const firstProvince = chinaAreaData[0];
  const firstCity = firstProvince.cities[0];
  return { province: firstProvince, city: firstCity };
};

/**
 * 通过IP定位获取城市信息
 * @returns Promise<CityMatchResult> 城市匹配结果
 */
export const getCityByLocation = async (): Promise<CityMatchResult> => {
  try {
    const locationData = await getLocationByIP();
    
    // 尝试匹配中国城市
    const matchResult = matchChineseCity(locationData.country, locationData.city);
    
    if (matchResult) {
      return matchResult;
    }

    // 如果没有匹配到，返回默认城市
    console.warn('未能匹配到中国城市，使用默认城市');
    return getDefaultCity();
  } catch (error) {
    console.error('定位获取城市失败:', error);
    // 定位失败时返回默认城市
    return getDefaultCity();
  }
};

/**
 * 格式化城市显示名称
 * @param province 省份
 * @param city 城市
 * @returns string 格式化后的显示名称
 */
export const formatCityDisplay = (province: Province, city: City): string => {
  // 直辖市特殊处理
  const municipalities = ['北京市', '上海市', '天津市', '重庆市'];
  if (municipalities.includes(province.name)) {
    return province.name;
  }

  // 特别行政区特殊处理
  if (province.name.includes('特别行政区')) {
    return city.name;
  }

  // 其他情况显示城市名
  return city.name;
};

/**
 * 验证城市选择是否有效
 * @param province 省份
 * @param city 城市
 * @returns boolean 是否有效
 */
export const validateCitySelection = (province: Province | null, city: City | null): boolean => {
  if (!province || !city) return false;
  
  // 检查城市是否属于该省份
  return province.cities.some(c => c.code === city.code);
};

/**
 * 根据省份代码查找省份
 * @param provinceCode 省份代码
 * @returns Province | null 省份信息
 */
export const findProvinceByCode = (provinceCode: string): Province | null => {
  return chinaAreaData.find(p => p.code === provinceCode) || null;
};

/**
 * 根据城市代码查找城市和省份
 * @param cityCode 城市代码
 * @returns CityMatchResult | null 匹配结果
 */
export const findCityByCode = (cityCode: string): CityMatchResult | null => {
  for (const province of chinaAreaData) {
    const city = province.cities.find(c => c.code === cityCode);
    if (city) {
      return { province, city };
    }
  }
  return null;
};
