/**
 * 词云缓存工具
 * 为每个聊天会话单独存储和管理词云数据
 */

// 词云项数据结构
export interface WordCloudItem {
  word: string;
  id: string;
  timestamp: number;
  source: string;
}

// 会话词云数据结构
interface ConversationWordCloud {
  conversationId: string;
  words: WordCloudItem[];
  lastUpdated: number;
}

// 缓存配置
const CACHE_CONFIG = {
  STORAGE_KEY: 'chat_word_clouds',
  MAX_CONVERSATIONS: 50, // 最多缓存50个会话的词云
  MAX_WORDS_PER_CONVERSATION: 20, // 每个会话最多20个词云
  CACHE_EXPIRY: 30 * 24 * 60 * 60 * 1000, // 30天过期
  CLEANUP_THRESHOLD: 0.8, // 80%容量时开始清理
};

/**
 * 获取所有缓存的词云数据
 */
const getAllWordClouds = (): Record<string, ConversationWordCloud> => {
  try {
    const stored = localStorage.getItem(CACHE_CONFIG.STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.error('读取词云缓存失败:', error);
    return {};
  }
};

/**
 * 保存所有词云数据到localStorage
 */
const saveAllWordClouds = (wordClouds: Record<string, ConversationWordCloud>): void => {
  try {
    localStorage.setItem(CACHE_CONFIG.STORAGE_KEY, JSON.stringify(wordClouds));
  } catch (error) {
    console.error('保存词云缓存失败:', error);
    // 如果保存失败，尝试清理后重试
    cleanExpiredWordClouds();
    try {
      localStorage.setItem(CACHE_CONFIG.STORAGE_KEY, JSON.stringify(wordClouds));
    } catch (retryError) {
      console.error('重试保存词云缓存仍然失败:', retryError);
    }
  }
};

/**
 * 清理过期的词云数据
 */
export const cleanExpiredWordClouds = (): void => {
  try {
    const allWordClouds = getAllWordClouds();
    const now = Date.now();
    const validWordClouds: Record<string, ConversationWordCloud> = {};

    // 过滤过期数据
    Object.entries(allWordClouds).forEach(([conversationId, wordCloud]) => {
      if (now - wordCloud.lastUpdated < CACHE_CONFIG.CACHE_EXPIRY) {
        validWordClouds[conversationId] = wordCloud;
      }
    });

    // 如果数据量仍然过多，保留最近的数据
    const conversationIds = Object.keys(validWordClouds);
    if (conversationIds.length > CACHE_CONFIG.MAX_CONVERSATIONS) {
      // 按最后更新时间排序，保留最新的
      const sortedIds = conversationIds.sort((a, b) => 
        validWordClouds[b].lastUpdated - validWordClouds[a].lastUpdated
      );
      
      const keepIds = sortedIds.slice(0, CACHE_CONFIG.MAX_CONVERSATIONS);
      const finalWordClouds: Record<string, ConversationWordCloud> = {};
      
      keepIds.forEach(id => {
        finalWordClouds[id] = validWordClouds[id];
      });
      
      saveAllWordClouds(finalWordClouds);
      console.log(`清理词云缓存，保留 ${keepIds.length} 个会话的数据`);
    } else {
      saveAllWordClouds(validWordClouds);
    }
  } catch (error) {
    console.error('清理词云缓存失败:', error);
  }
};

/**
 * 获取指定会话的词云数据
 */
export const getWordCloudForConversation = (conversationId: string): WordCloudItem[] => {
  if (!conversationId) return [];

  try {
    const allWordClouds = getAllWordClouds();
    const wordCloud = allWordClouds[conversationId];
    
    if (!wordCloud) return [];
    
    // 检查是否过期
    if (Date.now() - wordCloud.lastUpdated > CACHE_CONFIG.CACHE_EXPIRY) {
      return [];
    }
    
    return wordCloud.words || [];
  } catch (error) {
    console.error('获取会话词云失败:', conversationId, error);
    return [];
  }
};

/**
 * 保存指定会话的词云数据
 */
export const saveWordCloudForConversation = (conversationId: string, words: WordCloudItem[]): void => {
  if (!conversationId || !Array.isArray(words)) return;

  try {
    const allWordClouds = getAllWordClouds();
    
    // 限制词云数量
    const limitedWords = words.slice(-CACHE_CONFIG.MAX_WORDS_PER_CONVERSATION);
    
    allWordClouds[conversationId] = {
      conversationId,
      words: limitedWords,
      lastUpdated: Date.now(),
    };

    saveAllWordClouds(allWordClouds);
  } catch (error) {
    console.error('保存会话词云失败:', conversationId, error);
  }
};

/**
 * 向指定会话增量添加词云
 */
export const addWordsToConversation = (conversationId: string, newWords: string[], source: string = 'slots'): WordCloudItem[] => {
  if (!conversationId || !Array.isArray(newWords) || newWords.length === 0) {
    return getWordCloudForConversation(conversationId);
  }

  try {
    const existingWords = getWordCloudForConversation(conversationId);
    
    // 创建新的词云项
    const newItems: WordCloudItem[] = newWords.map(word => ({
      word: word.trim(),
      id: `${source}-${word}-${Date.now()}-${Math.random()}`,
      timestamp: Date.now(),
      source,
    })).filter(item => item.word.length > 0);

    // 去重：基于word内容
    const existingWordsSet = new Set(existingWords.map(item => item.word));
    const filteredNewItems = newItems.filter(item => !existingWordsSet.has(item.word));

    // 合并并限制数量
    const combined = [...existingWords, ...filteredNewItems];
    const limited = combined.slice(-CACHE_CONFIG.MAX_WORDS_PER_CONVERSATION);

    // 保存更新后的词云
    saveWordCloudForConversation(conversationId, limited);
    
    return limited;
  } catch (error) {
    console.error('添加词云失败:', conversationId, error);
    return getWordCloudForConversation(conversationId);
  }
};

/**
 * 清除指定会话的词云数据
 */
export const clearWordCloudForConversation = (conversationId: string): void => {
  if (!conversationId) return;

  try {
    const allWordClouds = getAllWordClouds();
    delete allWordClouds[conversationId];
    saveAllWordClouds(allWordClouds);
  } catch (error) {
    console.error('清除会话词云失败:', conversationId, error);
  }
};

/**
 * 获取词云缓存统计信息
 */
export const getWordCloudCacheStats = () => {
  try {
    const allWordClouds = getAllWordClouds();
    const conversationCount = Object.keys(allWordClouds).length;
    const totalWords = Object.values(allWordClouds).reduce((sum, cloud) => sum + cloud.words.length, 0);
    
    return {
      conversationCount,
      totalWords,
      averageWordsPerConversation: conversationCount > 0 ? Math.round(totalWords / conversationCount) : 0,
      oldestUpdate: Math.min(...Object.values(allWordClouds).map(cloud => cloud.lastUpdated)),
      newestUpdate: Math.max(...Object.values(allWordClouds).map(cloud => cloud.lastUpdated)),
    };
  } catch (error) {
    console.error('获取词云统计失败:', error);
    return {
      conversationCount: 0,
      totalWords: 0,
      averageWordsPerConversation: 0,
      oldestUpdate: 0,
      newestUpdate: 0,
    };
  }
};

/**
 * 清空所有词云缓存
 */
export const clearAllWordClouds = (): void => {
  try {
    localStorage.removeItem(CACHE_CONFIG.STORAGE_KEY);
    console.log('已清空所有词云缓存');
  } catch (error) {
    console.error('清空词云缓存失败:', error);
  }
};

/**
 * 调试函数：打印所有词云缓存数据
 */
export const debugWordCloudCache = (): void => {
  try {
    const allWordClouds = getAllWordClouds();
    console.log('=== 词云缓存调试信息 ===');
    console.log('缓存的会话数量:', Object.keys(allWordClouds).length);

    Object.entries(allWordClouds).forEach(([conversationId, wordCloud]) => {
      console.log(`会话 ${conversationId}:`, {
        词汇数量: wordCloud.words.length,
        最后更新: new Date(wordCloud.lastUpdated).toLocaleString(),
        词汇列表: wordCloud.words.map(w => w.word)
      });
    });

    const stats = getWordCloudCacheStats();
    console.log('缓存统计:', stats);
    console.log('=== 调试信息结束 ===');
  } catch (error) {
    console.error('调试词云缓存失败:', error);
  }
};

// 初始化时清理过期数据
cleanExpiredWordClouds();
