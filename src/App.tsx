/**
 * 主应用组件
 * 根据设备类型和认证状态显示对应页面
 */

import { useState, useEffect } from 'react';
import { AuthStatus, checkAuthStatus } from './utils/auth';
import PCNotSupported from './pages/PCNotSupported';
import Home from './pages/Home';
import Login from './pages/Login';
import ChatPage from './pages/ChatPage';
import TestComparison from './pages/TestComparison';
import CarDetailPage from './pages/CarDetailPage';
import SettingsPage from './pages/SettingsPage';
import CarRecommendationHistoryPage from './pages/CarRecommendationHistoryPage';
import CarComparison from './components/CarComparison';
import SpineTest from './pages/SpineTest';
import ErrorBoundary from './components/ErrorBoundary';


// 页面类型枚举
enum PageType {
  HOME = 'home',
  PHONE_LOGIN = 'phone_login',
  CAR_LOGIN = 'car_login',
  CAR_DETAIL = 'car_detail',
  SETTINGS = 'settings',
  CAR_RECOMMENDATION_HISTORY = 'car_recommendation_history',
  CAR_COMPARISON = 'car_comparison'
}

function App() {
  const [authStatus, setAuthStatus] = useState<AuthStatus>(AuthStatus.LOADING);
  const [currentPage, setCurrentPage] = useState<PageType>(PageType.HOME);
  const [selectedCarData, setSelectedCarData] = useState<any>(null);
  const [carDetailSourcePage, setCarDetailSourcePage] = useState<PageType>(PageType.HOME);
  const [comparisonData, setComparisonData] = useState<any[]>([]);
  const [chatScrollPosition, setChatScrollPosition] = useState<number | null>(null);

  // 检查认证状态
  const checkAuth = () => {
    const status = checkAuthStatus();
    setAuthStatus(status);
  };

  // 组件挂载时检查认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  // 检查是否是测试页面
  if (window.location.pathname === '/test-comparison') {
    return <TestComparison />;
  }

  // 检查是否是 Spine 测试页面
  if (window.location.pathname === '/spine-test') {
    return <SpineTest />;
  }



  // 登录成功回调
  const handleLoginSuccess = () => {
    setAuthStatus(AuthStatus.AUTHENTICATED);
  };

  // 退出登录回调
  const handleLogout = () => {
    setAuthStatus(AuthStatus.NEED_LOGIN);
    setCurrentPage(PageType.HOME);
  };

  // 页面导航处理函数
  const handleCarLoginClick = () => {
    // 这里可以添加汽车街登录逻辑
    alert('汽车街登录功能待开发');
  };

  const handlePhoneLoginClick = () => {
    setCurrentPage(PageType.PHONE_LOGIN);
  };

  const handleBackToHome = () => {
    setCurrentPage(PageType.HOME);
  };

  // 保存聊天页面滚动位置的函数（暂时保留供未来使用）
  // const saveChatScrollPosition = (position: number) => {
  //   setChatScrollPosition(position);
  // };

  // 车辆详情页面导航
  const handleCarDetailClick = (carData: any, sourcePage: PageType = PageType.HOME, scrollPosition?: number) => {
    console.log('🚗 进入车辆详情页，来源页面:', sourcePage);
    // 如果来源是聊天页面且提供了滚动位置，则保存
    if (sourcePage === PageType.HOME && scrollPosition !== undefined) {
      setChatScrollPosition(scrollPosition);
    }
    setSelectedCarData(carData);
    setCarDetailSourcePage(sourcePage);
    setCurrentPage(PageType.CAR_DETAIL);
  };

  const handleBackFromCarDetail = () => {
    console.log('🔙 从车辆详情页返回，目标页面:', carDetailSourcePage);
    setSelectedCarData(null);
    setCurrentPage(carDetailSourcePage); // 返回到来源页面
    // 注意：不在这里清除 chatScrollPosition，让 ChatPage 组件自己处理
  };

  // 设置页面导航
  const handleSettingsClick = () => {
    setCurrentPage(PageType.SETTINGS);
  };

  const handleBackFromSettings = () => {
    setCurrentPage(PageType.HOME); // 重置页面状态，让认证状态下显示聊天页面
  };

  // 车辆推荐记录页面导航
  const handleCarRecommendationHistoryClick = () => {
    setCurrentPage(PageType.CAR_RECOMMENDATION_HISTORY);
  };

  const handleBackFromCarRecommendationHistory = () => {
    setCurrentPage(PageType.SETTINGS); // 返回设置页面
  };

  // 车辆对比页面导航
  const handleCarComparisonClick = (cars: any[], scrollPosition?: number) => {
    console.log('🔄 对比按钮被点击，车辆数据:', cars);
    // 保存聊天页面的滚动位置
    if (scrollPosition !== undefined) {
      setChatScrollPosition(scrollPosition);
    }
    setComparisonData(cars);
    setCurrentPage(PageType.CAR_COMPARISON);
  };

  const handleBackFromCarComparison = () => {
    setComparisonData([]);
    setCurrentPage(PageType.HOME); // 返回聊天页面
    // 注意：不在这里清除 chatScrollPosition，让 ChatPage 组件自己处理
  };

  // 根据认证状态渲染对应页面
  switch (authStatus) {
    case AuthStatus.LOADING:
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
            <p className="text-gray-600">加载中...</p>
          </div>
        </div>
      );

    case AuthStatus.PC_NOT_SUPPORTED:
      return <PCNotSupported />;

    case AuthStatus.NEED_LOGIN:
      // 根据当前页面状态显示对应的登录页面
      switch (currentPage) {
        case PageType.HOME:
          return (
            <ErrorBoundary>
              <Home
                onCarLoginClick={handleCarLoginClick}
                onPhoneLoginClick={handlePhoneLoginClick}
              />
            </ErrorBoundary>
          );
        case PageType.PHONE_LOGIN:
          return (
            <ErrorBoundary>
              <Login
                onLoginSuccess={handleLoginSuccess}
                onBackClick={handleBackToHome}
              />
            </ErrorBoundary>
          );
        default:
          return (
            <ErrorBoundary>
              <Home
                onCarLoginClick={handleCarLoginClick}
                onPhoneLoginClick={handlePhoneLoginClick}
              />
            </ErrorBoundary>
          );
      }

    case AuthStatus.AUTHENTICATED:
      // 如果当前页面是车辆详情页面，显示车辆详情
      if (currentPage === PageType.CAR_DETAIL && selectedCarData) {
        return (
          <ErrorBoundary>
            <CarDetailPage
              carData={selectedCarData}
              onBack={handleBackFromCarDetail}
            />
          </ErrorBoundary>
        );
      }
      // 如果当前页面是设置页面，显示设置页面
      if (currentPage === PageType.SETTINGS) {
        return (
          <ErrorBoundary>
            <SettingsPage
              onBack={handleBackFromSettings}
              onLogout={handleLogout}
              onCarRecommendationHistoryClick={handleCarRecommendationHistoryClick}
            />
          </ErrorBoundary>
        );
      }
      // 如果当前页面是车辆推荐记录页面，显示车辆推荐记录页面
      if (currentPage === PageType.CAR_RECOMMENDATION_HISTORY) {
        return (
          <ErrorBoundary>
            <CarRecommendationHistoryPage
              onBack={handleBackFromCarRecommendationHistory}
              onCarDetailClick={(carData) => handleCarDetailClick(carData, PageType.CAR_RECOMMENDATION_HISTORY)}
            />
          </ErrorBoundary>
        );
      }
      // 如果当前页面是车辆对比页面，显示车辆对比页面
      if (currentPage === PageType.CAR_COMPARISON) {
        console.log('📱 渲染对比页面，车辆数据:', comparisonData);
        return (
          <ErrorBoundary>
            <CarComparison
              cars={comparisonData}
              onClose={handleBackFromCarComparison}
              onCarDetailClick={(carData) => handleCarDetailClick(carData, PageType.CAR_COMPARISON)}
            />
          </ErrorBoundary>
        );
      }
      // 否则显示聊天页面
      return (
        <ErrorBoundary>
          <ChatPage
            onLogout={handleLogout}
            onCarDetailClick={(carData, scrollPosition) => handleCarDetailClick(carData, PageType.HOME, scrollPosition)}
            onSettingsClick={handleSettingsClick}
            onCarComparisonClick={(cars, scrollPosition) => handleCarComparisonClick(cars, scrollPosition)}
            restoreScrollPosition={chatScrollPosition}
            onScrollPositionRestored={() => setChatScrollPosition(null)}
          />
        </ErrorBoundary>
      );

    default:
      return (
        <ErrorBoundary>
          <Home
            onCarLoginClick={handleCarLoginClick}
            onPhoneLoginClick={handlePhoneLoginClick}
          />
        </ErrorBoundary>
      );
  }
}

export default App;
